/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable import/no-unresolved */
import { useEffect, useRef } from "react"
import { useAuthStore } from "@/store/AuthenticationStore"
import { saveString, loadString, remove } from "@/utils/storage"
// import { navigationRef } from "@/navigators/navigationUtilities"
import NetInfo from "@react-native-community/netinfo"
import { useNetworkStore } from "@/store/useNetworkStore"

const TOKEN_CHECK_INTERVAL = 30000 // Check every 30 seconds
const MAX_TOKEN_AGE = 24 * 60 * 60 * 1000 // 24 hours in milliseconds

const LOGOUT_FLAG_KEY = "hasLoggedOut"

export const useTokenValidation = () => {
  const { logout, refreshAuthToken, isAuthenticated } = useAuthStore()
  const checkingRef = useRef(false)
  const networkStore = useNetworkStore()

  // Helper to check if already logged out
  const hasLoggedOut = () => loadString(LOGOUT_FLAG_KEY) === "true"
  const setLoggedOut = () => saveString(LOGOUT_FLAG_KEY, "true")
  const clearLoggedOut = () => remove(LOGOUT_FLAG_KEY)

  const validateToken = async () => {
    if (checkingRef.current) return
    checkingRef.current = true

    try {
      // Only run validation if authenticated and not already logged out
      if (!isAuthenticated || hasLoggedOut()) {
        checkingRef.current = false
        return
      }

      // Check network connection first
      const netInfo = await NetInfo.fetch()
      if (!netInfo.isConnected) {
        networkStore.checkInternet()
        checkingRef.current = false
        return
      }

      const accessToken = await loadString("accessToken")
      const refreshToken = await loadString("refreshToken")

      if (!accessToken || !refreshToken) {
        console.warn("❌ No tokens found, logging out...")
        handleLogout()
        checkingRef.current = false
        return
      }

      // Check token age
      const tokenTimestamp = await loadString("tokenTimestamp")
      if (tokenTimestamp) {
        const tokenAge = Date.now() - parseInt(tokenTimestamp)
        if (tokenAge > MAX_TOKEN_AGE) {
          console.warn("🔒 Token too old, logging out...")
          handleLogout()
          checkingRef.current = false
          return
        }
      }

      // Try to refresh the token
      await refreshAuthToken()
      // If successful, clear the logged out flag
      clearLoggedOut()
    } catch (error: any) {
      console.error("🔴 Token validation failed:", error)
      if (error.message === "No refresh token found") {
        console.log("🔴 No refresh token in validation, ending session...")
      }
      handleLogout()
    } finally {
      checkingRef.current = false
    }
  }

  const handleLogout = () => {
    setLoggedOut()
    logout()
    // Navigation is now handled in the logout function itself
  }

  useEffect(() => {
    if (!isAuthenticated || hasLoggedOut()) {
      setLoggedOut()
      handleLogout()
      return
    } else {
      clearLoggedOut()
    }

    // Initial check
    validateToken()

    // Set up interval for periodic checks only if authenticated and not logged out
    const interval = setInterval(() => {
      if (isAuthenticated && !hasLoggedOut()) {
        validateToken()
      }
    }, TOKEN_CHECK_INTERVAL)

    // Set up network listener only if authenticated and not logged out
    const unsubscribe = NetInfo.addEventListener((state) => {
      if (state.isConnected && isAuthenticated && !hasLoggedOut()) {
        validateToken()
      }
    })

    return () => {
      clearInterval(interval)
      unsubscribe()
    }
  }, [isAuthenticated])

  return validateToken
}
