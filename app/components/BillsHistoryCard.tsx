import { View, TouchableOpacity, ViewStyle, TextStyle } from "react-native"
import { Text, Icon } from "."
import { colors, spacing } from "@/theme"
import { formatDate } from "@/utils/dateUtils"

export interface BillHistoryItem {
  id: string
  type: "electricity" | "water" | "internet" | "phone" | "other"
  provider: string
  amount: number
  currency: string
  status: "pending" | "completed" | "failed"
  date: string
  meterNumber?: string
  reference?: string
}

interface BillsHistoryCardProps {
  bill: BillHistoryItem
  onPress?: (bill: BillHistoryItem) => void
  style?: ViewStyle
}

export const BillsHistoryCard: React.FC<BillsHistoryCardProps> = ({ bill, onPress, style }) => {
  const getBillTypeIcon = (type: BillHistoryItem["type"]) => {
    switch (type) {
      case "electricity":
        return "lightbulb"
      case "water":
        return "water"
      case "internet":
        return "transfer"
      case "phone":
        return "message"
      default:
        return "utilities"
    }
  }

  const getBillTypeColor = (type: BillHistoryItem["type"]) => {
    switch (type) {
      case "electricity":
        return colors.palette.accent500
      case "water":
        return colors.palette.primary500
      case "internet":
        return colors.palette.secondary500
      case "phone":
        return colors.palette.terciary100
      default:
        return colors.palette.neutral500
    }
  }

  const getStatusColor = (status: BillHistoryItem["status"]) => {
    switch (status) {
      case "completed":
        return colors.palette.accent200
      case "pending":
        return colors.palette.accent300
      case "failed":
        return colors.palette.accent400
      default:
        return colors.palette.neutral500
    }
  }

  const getStatusText = (status: BillHistoryItem["status"]) => {
    switch (status) {
      case "completed":
        return "Payé"
      case "pending":
        return "En cours"
      case "failed":
        return "Échoué"
      default:
        return "Inconnu"
    }
  }

  return (
    <TouchableOpacity style={[$card, style]} onPress={() => onPress?.(bill)} activeOpacity={0.8}>
      <View style={$cardHeader}>
        <View style={$billTypeContainer}>
          <View style={[$iconContainer, { backgroundColor: getBillTypeColor(bill.type) }]}>
            <Icon icon={getBillTypeIcon(bill.type)} size={20} color={colors.palette.neutral100} />
          </View>
          <View style={$billInfo}>
            <Text style={$billProvider}>{bill.provider}</Text>
            <Text style={$billType}>
              {bill.type === "electricity" && "Électricité"}
              {bill.type === "water" && "Eau"}
              {bill.type === "internet" && "Internet"}
              {bill.type === "phone" && "Téléphone"}
              {bill.type === "other" && "Autre"}
            </Text>
          </View>
        </View>
        <View style={[$statusContainer, { backgroundColor: getStatusColor(bill.status) }]}>
          <Text style={$statusText}>{getStatusText(bill.status)}</Text>
        </View>
      </View>

      <View style={$cardBody}>
        <View style={$amountContainer}>
          <Text style={$amountLabel}>Montant</Text>
          <Text style={$amount}>
            {bill.amount.toLocaleString()} {bill.currency}
          </Text>
        </View>

        <View style={$detailsContainer}>
          <View style={$detailItem}>
            <Text style={$detailLabel}>Date</Text>
            <Text style={$detailValue}>{formatDate(bill.date)}</Text>
          </View>
          {bill.meterNumber && (
            <View style={$detailItem}>
              <Text style={$detailLabel}>Compteur</Text>
              <Text style={$detailValue}>{bill.meterNumber}</Text>
            </View>
          )}
          {bill.reference && (
            <View style={$detailItem}>
              <Text style={$detailLabel}>Référence</Text>
              <Text style={$detailValue}>{bill.reference}</Text>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  )
}

const $card: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  borderRadius: 16,
  padding: spacing.md,
  marginBottom: spacing.sm,
  // Shadow for iOS
  shadowColor: colors.palette.neutral500,
  shadowOffset: { width: 0, height: 4 },
  shadowOpacity: 0.1,
  shadowRadius: 8,
  // Shadow for Android
  elevation: 4,
  borderWidth: 1,
  borderColor: colors.palette.neutral200,
}

const $cardHeader: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "flex-start",
  marginBottom: spacing.md,
}

const $billTypeContainer: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  flex: 1,
}

const $iconContainer: ViewStyle = {
  width: 40,
  height: 40,
  borderRadius: 12,
  justifyContent: "center",
  alignItems: "center",
  marginRight: spacing.sm,
}

const $billInfo: ViewStyle = {
  flex: 1,
}

const $billProvider: TextStyle = {
  fontSize: 16,
  fontWeight: "bold",
  color: colors.palette.neutral900,
  marginBottom: spacing.xs,
}

const $billType: TextStyle = {
  fontSize: 14,
  color: colors.palette.neutral600,
}

const $statusContainer: ViewStyle = {
  paddingHorizontal: spacing.sm,
  paddingVertical: spacing.xs,
  borderRadius: 8,
}

const $statusText: TextStyle = {
  fontSize: 12,
  fontWeight: "bold",
  color: colors.palette.neutral100,
}

const $cardBody: ViewStyle = {
  gap: spacing.sm,
}

const $amountContainer: ViewStyle = {
  marginBottom: spacing.xs,
}

const $amountLabel: TextStyle = {
  fontSize: 12,
  color: colors.palette.neutral600,
  marginBottom: spacing.xs,
}

const $amount: TextStyle = {
  fontSize: 20,
  fontWeight: "bold",
  color: colors.palette.neutral900,
}

const $detailsContainer: ViewStyle = {
  flexDirection: "row",
  flexWrap: "wrap",
  gap: spacing.md,
}

const $detailItem: ViewStyle = {
  flex: 1,
  minWidth: 120,
}

const $detailLabel: TextStyle = {
  fontSize: 12,
  color: colors.palette.neutral600,
  marginBottom: spacing.xs,
}

const $detailValue: TextStyle = {
  fontSize: 14,
  fontWeight: "500",
  color: colors.palette.neutral900,
}
