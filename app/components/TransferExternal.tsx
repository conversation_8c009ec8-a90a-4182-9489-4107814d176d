/* eslint-disable import/no-unresolved */
import { useState } from "react"
import { StyleProp, View, ViewStyle, Alert, TextStyle } from "react-native"
import { Button, CustomSelector, FencyTextInput, Icon, Text, FedhaLoader } from "."
import { $Gstyles, colors, spacing } from "@/theme"
import { useStores } from "@/store/rootStore"
import { useForm, Controller } from "react-hook-form"
import { WithdrawRequest } from "@/services/api"

interface WithdrawFormData {
  amount: string
  receiver_phone: string
  network: string
}

export interface TransferExternalProps {
  style?: StyleProp<ViewStyle>
}

export const TransferExternal = (props: TransferExternalProps) => {
  const {
    fedhapochi: { wallet, fetchWallet },
    appsettings: { currency, getExchangeRate },
    fedhapochi: { currentBalance },
  } = useStores()

  const { style } = props
  const $styles = [$container, style]

  const [isLoading, setIsLoading] = useState(false)

  const {
    control,
    handleSubmit,
    formState: { errors },
    // watch,
    reset,
  } = useForm<WithdrawFormData>({
    defaultValues: {
      amount: "",
      receiver_phone: "",
      network: "",
    },
  })

  const walletOptions = [
    { label: "Vodacom", value: "mpesa" },
    { label: "AIRTEL", value: "airtel" },
    { label: "ORANGE", value: "orange" },
  ]

  // Currencies are now loaded at app startup
  // No need to fetch currencies here

  const onSubmit = async (data: WithdrawFormData) => {
    try {
      setIsLoading(true)

      const response = await WithdrawRequest({
        amount: Number(data.amount),
        receiver_phone: data.receiver_phone,
        currency,
        network: data.network,
        note: "External Withdrawal",
      })

      if (response.success) {
        Alert.alert("Succès", "Votre demande de retrait a été traitée avec succès", [
          {
            text: "OK",
            onPress: async () => {
              reset() // Reset form
              await fetchWallet() // Refresh wallet balance
            },
          },
        ])
      } else {
        throw new Error(response.message || "Échec du retrait")
      }
    } catch (error: any) {
      Alert.alert(
        "Erreur",
        error.message || "Une erreur s'est produite lors du retrait. Veuillez réessayer.",
      )
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <View style={$styles}>
      <View style={$content}>
        <Text text="À partir de" />
        <View style={$Gstyles.boxcomp}>
          <View>
            <Icon
              icon="fedhapochi"
              color={colors.palette.primary600}
              size={30}
              containerStyle={$ActionIcon}
            />
          </View>

          <View>
            <Text preset="subheading" style={$Gstyles.serviceName}>
              {`${wallet?.name}`}{" "}
            </Text>
            <View>
              <Text preset="formHelper">{`${currency} ${wallet?.balance}`}</Text>
            </View>
          </View>
        </View>
        <Text text="Vers" />

        <Controller
          control={control}
          name="amount"
          rules={{
            required: "Le montant est obligatoire",
            validate: (value) => {
              const amount = parseFloat(value)
              if (isNaN(amount)) return "Le montant doit être un nombre valide"

              // Get exchange rate and convert amounts using API rates
              if (currency === "USD") {
                if (amount < 2) return "Le montant minimum est de 2 USD"
                // Convert currentBalance to USD using API exchange rate
                const balanceInUSD = getExchangeRate("FC", "USD") * currentBalance
                if (amount > balanceInUSD) {
                  return `Solde insuffisant. Votre solde est de ${balanceInUSD.toFixed(2)} USD`
                }
              } else if (currency === "FC") {
                if (amount < 5000) return "Le montant minimum est de 5000 FC"
                // Use currentBalance directly for FC
                if (amount > currentBalance) {
                  return `Solde insuffisant. Votre solde est de ${currentBalance.toFixed(2)} FC`
                }
              }
              return true
            },
          }}
          render={({ field: { onChange, value } }) => (
            <View style={$inputContainer}>
              <FencyTextInput
                value={value}
                onChange={onChange}
                inputname="Montant"
                keyboardType="numeric"
                placeholder="Saisir le montant"
                style={$input}
                helper={errors.amount?.message}
              />
              {errors.amount && <Text style={$errorText}>{errors.amount.message}</Text>}
            </View>
          )}
        />
        <Controller
          control={control}
          name="network"
          rules={{ required: "Le réseau est requis" }}
          render={({ field: { onChange, value } }) => (
            <CustomSelector
              inputname="Sélectionnez le portefeuille"
              value={value}
              onValueChange={onChange}
              isitems={walletOptions}
              placeholder={{ label: "Sélectionnez le réseau", value: null }}
              inputstyle={$selectorStyle}
            />
          )}
        />

        <Controller
          control={control}
          name="receiver_phone"
          rules={{
            required: "Le numéro de téléphone est requis",
            pattern: {
              value: /^[0-9]{9,12}$/,
              message: "Numéro de téléphone invalide",
            },
          }}
          render={({ field: { onChange, value } }) => (
            <View style={$inputContainer}>
              <FencyTextInput
                value={value}
                maxLength={9}
                onChange={onChange}
                inputname="Numéro de téléphone"
                keyboardType="numeric"
                placeholder="Entrez le numéro"
                style={$input}
                status={errors.receiver_phone?.message ? "error" : undefined}
              />
            </View>
          )}
        />
      </View>

      <View style={$footer}>
        <Button
          preset="reversed"
          text={isLoading ? "Traitement..." : "Transférer"}
          style={$transferButton}
          onPress={handleSubmit(onSubmit)}
          disabled={isLoading}
        />
      </View>

      {isLoading && (
        <View style={$loaderContainer}>
          <FedhaLoader />
        </View>
      )}
    </View>
  )
}

const $container: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  height: "100%",
}

const $content: ViewStyle = {
  paddingHorizontal: spacing.lg,
  paddingTop: spacing.xl,
}

const $inputContainer: ViewStyle = {
  marginBottom: spacing.lg,
  width: "100%",
}

const $input: ViewStyle = {
  width: "100%",
}

const $footer: ViewStyle = {
  position: "absolute",
  bottom: 0,
  left: 0,
  right: 0,
  paddingHorizontal: spacing.lg,
  paddingVertical: spacing.lg,
  borderTopWidth: 1,
  borderTopColor: colors.palette.neutral200,
  backgroundColor: colors.palette.neutral100,
}

const $transferButton: ViewStyle = {
  backgroundColor: colors.palette.neutral900,
  height: 48,
  borderRadius: 20,
}

const $selectorStyle: ViewStyle = {
  width: "100%",
}

const $ActionIcon: ViewStyle = {
  padding: 10,

  marginRight: 20,
  alignItems: "center",
  justifyContent: "center",
  alignContent: "center",
  // backgroundColor: colors.palette.accent300,
  borderRadius: 50,
}

const $loaderContainer: ViewStyle = {
  position: "absolute",
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  backgroundColor: "rgba(255, 255, 255, 0.7)",
  justifyContent: "center",
  alignItems: "center",
}
const $errorText: TextStyle = {
  color: "red",
  fontSize: 12,
  marginBottom: 5,
}
