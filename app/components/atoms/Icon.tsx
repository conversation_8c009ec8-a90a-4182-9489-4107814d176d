import { ComponentType } from "react"
import {
  Image,
  ImageStyle,
  StyleProp,
  TouchableOpacity,
  TouchableOpacityProps,
  View,
  ViewProps,
  ViewStyle,
} from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"

export type IconTypes = keyof typeof iconRegistry

interface IconProps extends TouchableOpacityProps {
  /**
   * The name of the icon
   */
  icon: IconTypes

  /**
   * An optional tint color for the icon
   */
  color?: string

  /**
   * An optional size for the icon. If not provided, the icon will be sized to the icon's resolution.
   */
  size?: number

  /**
   * Style overrides for the icon image
   */
  style?: StyleProp<ImageStyle>

  /**
   * Style overrides for the icon container
   */
  containerStyle?: StyleProp<ViewStyle>

  /**
   * An optional function to be called when the icon is pressed
   */
  onPress?: TouchableOpacityProps["onPress"]
}

/**
 * A component to render a registered icon.
 * It is wrapped in a <TouchableOpacity /> if `onPress` is provided, otherwise a <View />.
 * @see [Documentation and Examples]{@link https://docs.infinite.red/ignite-cli/boilerplate/app/components/Icon/}
 * @param {IconProps} props - The props for the `Icon` component.
 * @returns {JSX.Element} The rendered `Icon` component.
 */
export function Icon(props: IconProps) {
  const {
    icon,
    color,
    size,
    style: $imageStyleOverride,
    containerStyle: $containerStyleOverride,
    ...WrapperProps
  } = props

  const isPressable = !!WrapperProps.onPress
  const Wrapper = (WrapperProps?.onPress ? TouchableOpacity : View) as ComponentType<
    TouchableOpacityProps | ViewProps
  >

  const { theme } = useAppTheme()

  const $imageStyle: StyleProp<ImageStyle> = [
    $imageStyleBase,
    { tintColor: color ?? theme.colors.text },
    size !== undefined && { width: size, height: size },
    $imageStyleOverride,
  ]

  return (
    <Wrapper
      accessibilityRole={isPressable ? "imagebutton" : undefined}
      {...WrapperProps}
      style={$containerStyleOverride}
    >
      <Image style={$imageStyle} source={iconRegistry[icon]} />
    </Wrapper>
  )
}

const basePath = "../../../assets/icons/"

export const iconRegistry = {
  back: require(basePath + "back.png"),
  bell: require(basePath + "bell.png"),
  caretLeft: require(basePath + "caretLeft.png"),
  shelves: require(basePath + "shelves.png"),
  dialpad: require(basePath + "dial-pad.png"),
  caretRight: require(basePath + "caretRight.png"),
  check: require(basePath + "check.png"),
  utilities: require(basePath + "utilities.png"),
  notcertified: require(basePath + "patent.png"),
  certificate: require(basePath + "certificate.png"),
  donwload: require(basePath + "download.png"),
  giftcard: require(basePath + "promo-code.png"),
  facebook: require(basePath + "facebook.png"),
  email: require(basePath + "email.png"),
  store: require(basePath + "store.png"),
  hmenu: require(basePath + "hmenu.png"),
  invoice: require(basePath + "invoice.png"),
  realtor: require(basePath + "realtor.png"),
  briefcase: require(basePath + "briefcase.png"),
  moneyIn: require(basePath + "moneyIn.png"),
  instagram: require(basePath + "instagram.png"),
  delete: require(basePath + "delete.png"),
  shoppingBag: require(basePath + "shopping-cart.png"),
  addedincart: require(basePath + "addInshopping-bag.png"),
  linkedin: require(basePath + "linkedin.png"),
  twitter: require(basePath + "twitter.png"),
  notif: require(basePath + "notif.png"),
  hidden: require(basePath + "hidden.png"),
  staff: require(basePath + "staff.png"),
  reports: require(basePath + "reports.png"),
  trash: require(basePath + "trash.png"),
  verification: require(basePath + "verification.png"),
  percentage: require(basePath + "percentage.png"),
  agents: require(basePath + "agents.png"),
  lock: require(basePath + "lock.png"),
  elimu: require(basePath + "mortarboard.png"),
  monytrans: require(basePath + "money-transfer.png"),
  cashpower: require(basePath + "power-meter.png"),
  cashmoney: require(basePath + "cashmoney.png"),
  ExternalWallet: require(basePath + "ExternalWallet.png"),
  menu: require(basePath + "menu.png"),
  sel: require(basePath + "mobile-shopping.png"),
  group: require(basePath + "group.png"),
  arrwup: require(basePath + "arrwup.png"),
  FC: require(basePath + "FrankCongolais.png"),
  increase: require(basePath + "increase.png"),
  decrease: require(basePath + "decrease.png"),
  dollar: require(basePath + "dollar.png"),
  heart: require(basePath + "heart.png"),
  likefull: require(basePath + "likefull.png"),
  video: require(basePath + "video.png"),
  billspayment: require(basePath + "billspayment.png"),
  more: require(basePath + "more.png"),
  refresharrow: require(basePath + "refresharrow.png"),
  products: require(basePath + "products.png"),
  faildwifi: require(basePath + "faildwifi.png"),
  arwRigh: require(basePath + "caretRight.png"),
  faildqrcode: require(basePath + "faildqrcode.png"),
  warning: require(basePath + "warning.png"),
  report: require(basePath + "report.png"),
  trylater: require(basePath + "trylater.png"),
  settings: require(basePath + "adjustment.png"),
  pay: require(basePath + "pay.png"),
  history: require(basePath + "schedule.png"),
  view: require(basePath + "view.png"),
  mobmoney: require(basePath + "mobmoney.png"),
  home: require(basePath + "dashhome.png"),
  whatsapp: require(basePath + "whatsapp.png"),
  botchat: require(basePath + "bot.png"),
  scan: require(basePath + "tabscan.png"),
  accountV: require(basePath + "personinfo.png"),
  wallet: require(basePath + "wallet.png"),
  custmrsupport: require(basePath + "customer-support.png"),
  grid: require(basePath + "collage.png"),
  airtel: require(basePath + "airtel.png"),
  transfer: require(basePath + "envoyer.png"),
  topup: require(basePath + "topup.png"),
  bin: require(basePath + "bin.png"),
  minus: require(basePath + "minus.png"),
  fingerprint: require(basePath + "fingerprint.png"),
  drcFlg: require(basePath + "cdf.png"),
  fedhapochi: require(basePath + "fedhapochi.png"),
  downarrow: require(basePath + "down-arrow.png"),
  withdrawal: require(basePath + "withdrawal.png"),
  orderHistory: require(basePath + "navhistory.png"),
  canalplus: require(basePath + "canalplus.png"),
  plus: require(basePath + "plus.png"),
  qrcodewinc: require(basePath + "qrcodewinc.png"),
  smartphone: require(basePath + "smartphone.png"),
  noIcon: require(basePath + "noIcon.png"),
  subscription: require(basePath + "subscription.png"),
  tempblockcard: require(basePath + "tempblockcard.png"),
  updatelimits: require(basePath + "updatelimits.png"),
  flashOn: require(basePath + "flashOn.png"),
  menugrid: require(basePath + "menu-button.png"),
  nocamera: require(basePath + "no-camera.png"),
  identification: require(basePath + "identification.png"),
  cdf: require(basePath + "cdf.png"),
  privacy: require(basePath + "privacy.png"),
  idcardVerif: require(basePath + "id-cardVer.png"),
  otp: require(basePath + "otp.png"),
  contract: require(basePath + "contract.png"),
  loading: require(basePath + "loading.png"),
  edit: require(basePath + "edit.png"),
  info: require(basePath + "info.png"),
  Rcheck: require(basePath + "Rcheck.png"),
  map: require(basePath + "map.png"),
  conversation: require(basePath + "conversation.png"),
  personinfo: require(basePath + "personinfo.png"),
  upload_image: require(basePath + "upload_image.png"),
  addwallet: require(basePath + "addwallet.png"),
  question: require(basePath + "question.png"),
  backicon: require(basePath + "back_icon.png"),
  passport: require(basePath + "passport.png"),
  calendar: require(basePath + "calendar.png"),
  cancelround: require(basePath + "Faildcancel.png"),
  photocamera: require(basePath + "photo-camera.png"),
  wtrans: require(basePath + "wtrans.png"),
  idcard: require(basePath + "idcard.png"),
  logout: require(basePath + "out.png"),
  legaldocument: require(basePath + "legal-document.png"),
  share: require(basePath + "share.png"),
  reportDown: require(basePath + "reportDown.png"),
  contactmail: require(basePath + "communicate.png"),
  contactus: require(basePath + "contact-mail.png"),
  play: require(basePath + "play.png"),
  paym: require(basePath + "fpay.png"),
  credidcard: require(basePath + "credit-card.png"),
  search: require(basePath + "search.png"),
  T0clock: require(basePath + "30clock.png"),
  helpcenter: require(basePath + "live-chat.png"),
  message: require(basePath + "message.png"),
  taxi: require(basePath + "taxi.png"),
  lightbulb: require(basePath + "lightbulb-on-outline-inside-a-circle.png"),
  water: require(basePath + "water-drop.png"),
  nocontent: require(basePath + "nocontent.png"),
  flashOff: require(basePath + "flashOff.png"),
  tax: require(basePath + "tax.png"),
  user: require(basePath + "user.png"),
  x: require(basePath + "x.png"),
}

const $imageStyleBase: ImageStyle = {
  resizeMode: "contain",
}
