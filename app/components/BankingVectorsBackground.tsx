/* eslint-disable no-restricted-imports */
// import { colors } from "@/theme"
import React from "react"
import { View, ViewStyle } from "react-native"

import Svg, { Circle, Rect, G } from "react-native-svg"

export interface BankingVectorsBackgroundProps {
  style?: ViewStyle
}

export const BankingVectorsBackground: React.FC<BankingVectorsBackgroundProps> = ({ style }) => {
  return (
    <View style={[$container, style]}>
      <Svg width="100%" height="100%" viewBox="0 0 400 250" style={$svg}>
        <Rect x="0" y="0" width="400" height="250" fill="url(#backgroundGradient)" />
        <G transform="translate(320, 40)">
          <Rect
            x="0"
            y="0"
            width="20"
            height="20"
            rx="4"
            fill="url(#vectorGradient)"
            stroke="#9CA3AF"
            strokeWidth="0.5"
            opacity="0.4"
          />
        </G>

        <G transform="translate(0, 60)">
          <Rect
            x="0"
            y="0"
            width="20"
            height="20"
            rx="4"
            fill="url(#vectorGradient)"
            stroke="#9CA3AF"
            strokeWidth="1"
            opacity="0.4"
          />
        </G>
        <G transform="translate(100, 10)">
          <Rect
            x="0"
            y="0"
            width="20"
            height="20"
            rx="2"
            fill="url(#vectorGradient)"
            stroke="#9CA3AF"
            strokeWidth="0.5"
            opacity="0.5"
          />
        </G>

        <G transform="translate(300, 30)">
          <Circle
            cx="40"
            cy="10"
            r="10"
            fill="url(#vectorGradient)"
            stroke="#9CA3AF"
            strokeWidth="0.5"
            opacity="0.5"
          />
        </G>

        {/* Top left - Square */}
        <G transform="translate(40, 50)">
          <Rect
            x="0"
            y="0"
            width="30"
            height="30"
            rx="3"
            fill="url(#vectorGradient)"
            stroke="#9CA3AF"
            strokeWidth="0.5"
            opacity="0.4"
          />
        </G>

        {/* Middle left - Circle */}
        <G transform="translate(69, 140)">
          <Circle
            cx="20"
            cy="20"
            r="10"
            fill="url(#vectorGradient)"
            stroke="#9CA3AF"
            strokeWidth="0.5"
            opacity="0.5"
          />
        </G>

        <G transform="translate(310, 90)">
          <Rect
            x="0"
            y="0"
            width="30"
            height="30"
            rx="3"
            fill="url(#vectorGradient)"
            stroke="#9CA3AF"
            strokeWidth="0.5"
            opacity="0.5"
          />
        </G>

        <G transform="translate(395, 170)">
          <Rect
            x="0"
            y="0"
            width="30"
            height="30"
            rx="7"
            fill="url(#vectorGradient)"
            stroke="#9CA3AF"
            strokeWidth="0.5"
            opacity="0.5"
          />
        </G>

        {/* Bottom center - Square */}
        <G transform="translate(180, 200)">
          <Rect
            x="0"
            y="0"
            width="20"
            height="20"
            rx="2"
            fill="url(#vectorGradient)"
            stroke="#9CA3AF"
            strokeWidth="0.5"
            opacity="0.5"
          />
        </G>

        {/* Center - Circle */}
        <G transform="translate(200, 100)">
          <Circle
            cx="15"
            cy="15"
            r="10"
            fill="url(#vectorGradient)"
            stroke="#9CA3AF"
            strokeWidth="0.5"
            opacity="0.5"
          />
        </G>

        {/* Small decorative elements */}
        <Circle cx="120" cy="80" r="3" fill="#9CA3AF" opacity="0.15" />
        <Circle cx="280" cy="120" r="2" fill="#9CA3AF" opacity="0.12" />
        <Circle cx="80" cy="220" r="2.5" fill="#9CA3AF" opacity="0.1" />
        <Circle cx="320" cy="80" r="1.5" fill="#9CA3AF" opacity="0.08" />
        <Circle cx="150" cy="180" r="2" fill="#9CA3AF" opacity="0.1" />
      </Svg>
    </View>
  )
}

const $container: ViewStyle = {
  position: "absolute",
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  zIndex: 0,
}

const $svg: ViewStyle = {
  position: "absolute",
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
}
