import { useEffect, useState } from "react"
import { View, Modal, TouchableOpacity, ViewStyle, TextStyle } from "react-native"
import { Text, Icon } from "."
import { colors, spacing } from "@/theme"
import NetInfo from "@react-native-community/netinfo"

interface NetworkOverlayProps {
  isVisible: boolean
  onRetry: () => void
  onReload: () => void
}

export const NetworkOverlay: React.FC<NetworkOverlayProps> = ({ isVisible, onRetry, onReload }) => {
  const [retryCount, setRetryCount] = useState(0)
  const [isChecking, setIsChecking] = useState(false)
  const MAX_RETRY_ATTEMPTS = 3

  useEffect(() => {
    if (isVisible) {
      setRetryCount(0)
    }
  }, [isVisible])

  const handleRetry = async () => {
    if (retryCount >= MAX_RETRY_ATTEMPTS) {
      onReload()
      return
    }

    setIsChecking(true)
    setRetryCount((prev) => prev + 1)

    try {
      // Check network connection
      const netInfo = await NetInfo.fetch()
      if (netInfo.isConnected) {
        onRetry()
      } else {
        // Wait 2 seconds before allowing another retry
        setTimeout(() => {
          setIsChecking(false)
        }, 2000)
      }
    } catch (error) {
      console.error("Network check failed:", error)
      setTimeout(() => {
        setIsChecking(false)
      }, 2000)
    }
  }

  const handleReload = () => {
    setRetryCount(0)
    onReload()
  }

  return (
    <Modal visible={isVisible} animationType="fade" transparent={true}>
      <View style={$overlay}>
        <View style={$container}>
          <View style={$iconContainer}>
            <Icon icon="faildwifi" size={80} color={colors.palette.accent400} />
          </View>

          <Text style={$title}>Pas de connexion internet</Text>

          <Text style={$message}>Vérifiez votre connexion internet et réessayez.</Text>

          {/* {retryCount > 0 && (
            <Text style={$retryInfo}>
              Tentative {retryCount}/{MAX_RETRY_ATTEMPTS}
            </Text>
          )} */}

          <View style={$buttonContainer}>
            {retryCount < MAX_RETRY_ATTEMPTS ? (
              <TouchableOpacity
                style={[$retryButton, isChecking && $retryButtonDisabled]}
                onPress={handleRetry}
                disabled={isChecking}
              >
                <Icon
                  icon={isChecking ? "loading" : "refresharrow"}
                  size={20}
                  color={colors.palette.neutral100}
                />
                <Text style={$retryButtonText}>{isChecking ? "Vérification..." : "Réessayer"}</Text>
              </TouchableOpacity>
            ) : (
              <TouchableOpacity style={$reloadButton} onPress={handleReload}>
                <Icon icon="refresharrow" size={20} color={colors.palette.neutral100} />
                <Text style={$reloadButtonText}>Recharger l&lsquo;application</Text>
              </TouchableOpacity>
            )}
          </View>

          <Text style={$tip}>
            💡 Conseil: Vérifiez que votre WiFi ou données mobiles sont activés
          </Text>
        </View>
      </View>
    </Modal>
  )
}

const $overlay: ViewStyle = {
  flex: 1,
  backgroundColor: colors.palette.overlay50,
  justifyContent: "center",
  alignItems: "center",
}

const $container: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  borderRadius: 20,
  padding: spacing.xl,
  margin: spacing.lg,
  alignItems: "center",
  maxWidth: 350,
  // Shadow for iOS
  shadowColor: colors.palette.neutral900,
  shadowOffset: { width: 0, height: 10 },
  shadowOpacity: 0.3,
  shadowRadius: 20,
  // Shadow for Android
  elevation: 10,
}

const $iconContainer: ViewStyle = {
  marginBottom: spacing.lg,
}

const $title: TextStyle = {
  fontSize: 24,
  fontWeight: "bold",
  color: colors.palette.neutral900,
  textAlign: "center",
  marginBottom: spacing.sm,
}

const $message: TextStyle = {
  fontSize: 16,
  color: colors.palette.neutral600,
  textAlign: "center",
  marginBottom: spacing.lg,
  lineHeight: 22,
}

// const $retryInfo: TextStyle = {
//   fontSize: 14,
//   color: colors.palette.accent400,
//   textAlign: "center",
//   marginBottom: spacing.lg,
//   fontWeight: "500",
// }

const $buttonContainer: ViewStyle = {
  width: "100%",
  marginBottom: spacing.lg,
}

const $retryButton: ViewStyle = {
  backgroundColor: colors.palette.primary500,
  borderRadius: 12,
  paddingVertical: spacing.md,
  paddingHorizontal: spacing.lg,
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "center",
  gap: spacing.sm,
}

const $retryButtonDisabled: ViewStyle = {
  backgroundColor: colors.palette.neutral400,
}

const $retryButtonText: TextStyle = {
  fontSize: 16,
  fontWeight: "bold",
  color: colors.palette.neutral100,
}

const $reloadButton: ViewStyle = {
  backgroundColor: colors.palette.accent500,
  borderRadius: 12,
  paddingVertical: spacing.md,
  paddingHorizontal: spacing.lg,
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "center",
  gap: spacing.sm,
}

const $reloadButtonText: TextStyle = {
  fontSize: 16,
  fontWeight: "bold",
  color: colors.palette.neutral100,
}

const $tip: TextStyle = {
  fontSize: 12,
  color: colors.palette.neutral500,
  textAlign: "center",
  fontStyle: "italic",
}
