/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-restricted-imports */
/* eslint-disable react-native/sort-styles */
/* eslint-disable react-native/no-unused-styles */
/* eslint-disable react-native/no-inline-styles */
/* eslint-disable react-native/no-color-literals */
import React, { FC, useState } from "react"
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
  TextStyle,
  Image,
  ImageStyle,
} from "react-native"
import { Button } from "./atoms/Button"
import { Icon } from "."
import { colors } from "@/theme"
// Assuming you have an icon component setup (e.g., using react-native-vector-icons)
// import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

// --- Define Brand Colors ---
const brandColors = {
  pastelTeal: "#A0CDCD",
  coral: "#F27457",
  brightTeal: "#03A6A6",
  darkTeal: "#0D5152",
  white: "#FFFFFF",
  lightGrey: "#F0F0F0", // For subtle backgrounds/borders
  mediumGrey: "#888888", // For secondary text
}

const mockBalance = {
  cdf: 882196.67, // Inspired by image's MDL value
  usd: 1231.32, // From Image
  // gbp: 7371.12 // Optional, from image
}

// --- Define Props Interface ---
interface StoreWidgetCardProps {
  /** The main name of the store */
  storeName: string
  /** Optional: Type or category of the store */
  storeType?: string
  /** Optional: Name of the icon to display (from your icon library) */
  iconName?: any
  /** Optional: Color for the icon, defaults to brightTeal */
  iconColor?: string
  /** Optional: Label for the key metric (e.g., "Status", "Revenue") */
  metricLabel?: string
  /** Optional: Value for the key metric */
  metricValue?: string | number
  /** Optional: Specific color for the metric value (e.g., for status) */
  metricValueColor?: string
  /** Optional: Function to execute when the card is pressed */
  onPress?: () => void
  /** Optional: Custom styles for the outer container */
  style?: ViewStyle
}

// --- The Component ---
export const StoreWidgetCard: FC<StoreWidgetCardProps> = ({
  storeName,
  storeType,
  iconName = "store", // Default icon if none provided
  iconColor = brandColors.brightTeal,
  // metricLabel,
  style,
}) => {
  // const CardComponent = onPress ? TouchableOpacity : View

  // const [selectedBusiness] = useState(mockBusiness)
  const [balance] = useState(mockBalance)
  const [isBalanceHidden, setIsBalanceHidden] = useState(false)

  // --- Handlers ---
  const handleSelectBusiness = () => {
    console.log("Trigger Business Selection Modal")
    // TODO: Implement modal logic here
    // setIsModalVisible(true);
  }

  return (
    <View style={[styles.cardContainer, style]}>
      <TouchableOpacity style={styles.contentWrapper} onPress={handleSelectBusiness}>
        {/* Optional Icon Area */}
        {iconName && (
          <View style={[styles.iconContainer, { backgroundColor: brandColors.lightGrey }]}>
            {typeof iconName === "string" ? (
              <Text style={{ fontSize: 20, color: iconColor }}>
                {iconName.substring(0, 1).toUpperCase()}
              </Text>
            ) : typeof iconName === "object" && iconName.uri ? (
              <Image source={iconName} style={styles.iconImage} resizeMode="cover" />
            ) : (
              <Text style={{ fontSize: 20, color: iconColor }}>S</Text>
            )}
          </View>
        )}

        {/* Text Content Area */}
        <View style={styles.textContainer}>
          <Text style={styles.storeName} numberOfLines={1} ellipsizeMode="tail">
            {storeName}
          </Text>
          {storeType && (
            <Text style={styles.storeType} numberOfLines={1} ellipsizeMode="tail">
              {storeType}
            </Text>
          )}
        </View>
        {/* <Icon icon="downarrow" size={17} color="#FFF" /> */}
      </TouchableOpacity>
      <View style={styles.balanceSection}>
        <View style={styles.balanceHeader}>
          <Text style={styles.primaryBalance}>
            {isBalanceHidden
              ? "******"
              : `${balance.cdf.toLocaleString("fr-CD", {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })} CDF`}
          </Text>
          <TouchableOpacity onPress={() => setIsBalanceHidden(!isBalanceHidden)}>
            <Icon icon={isBalanceHidden ? "view" : "hidden"} size={20} color="#FFF" />
          </TouchableOpacity>
        </View>

        <View style={styles.secondaryBalanceContainer}>
          <Text style={styles.secondaryBalanceText}>
            {isBalanceHidden
              ? "******"
              : `${balance.usd.toLocaleString("en-US", {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })} USD`}
          </Text>
          <Text style={styles.secondaryBalanceSeparator}>|</Text>
        </View>
      </View>
      <Button
        text="Retirer les fonds"
        preset="reversed"
        style={styles.withdrawButton}
        onPress={() => console.log("Navigate to withdraw screen")}
      />
    </View>
  )
}

// --- Styles ---
const styles = StyleSheet.create({
  cardContainer: {
    backgroundColor: colors.palette.neutral900,
    borderRadius: 16,
    padding: 15,
    alignItems: "center",
  } as ViewStyle,
  contentWrapper: {
    alignItems: "center",
    flexDirection: "row",
    flex: 1, // Ensure wrapper takes available space
  },
  // Balance Section
  balanceSection: {
    alignItems: "flex-start", // Align text left as in image
    marginBottom: 15,
    paddingVertical: 30, // Slight padding for better look
    paddingHorizontal: 5, // Slight indent if needed
    width: "100%",
  },
  balanceHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    width: "100%",
    marginBottom: 10,
  },

  primaryBalance: {
    fontSize: 30,
    fontWeight: "bold",
    color: colors.palette.neutral200, // Dark text for light theme
    // marginBottom: ,
    letterSpacing: 0.5,
  },
  secondaryBalanceContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  secondaryBalanceText: {
    fontSize: 14,
    color: colors.palette.neutral400, // Medium grey text
    fontWeight: "500",
  },
  secondaryBalanceSeparator: {
    fontSize: 14,
    color: colors.palette.neutral600, // Light separator
    marginHorizontal: 8,
  },
  // Withdraw Button
  withdrawButton: {
    // borderWidth: 1,
    backgroundColor: colors.palette.secondsMain,
    borderRadius: 16,
    paddingVertical: 16, // Button preset might handle padding
    width: "100%",
  },
  iconContainer: {
    width: 45,
    height: 45,
    borderRadius: 22.5, // Make it circular
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
    backgroundColor: colors.palette.neutral200, // Use a light background for the icon circle
  } as ViewStyle,
  metricValue: {
    fontSize: 14,
    fontWeight: "700", // Bold
    // color is set dynamically
  } as TextStyle,
  storeName: {
    fontSize: 16,
    fontWeight: "600", // Semibold
    color: colors.palette.neutral200,
    marginBottom: 2,
  } as TextStyle,
  storeType: {
    fontSize: 13,
    color: colors.palette.accent300, // Use grey for less emphasis
  } as TextStyle,
  textContainer: {
    flex: 1, // Allow text to take up available space and truncate
    marginRight: 10, // Space before metric
    justifyContent: "center", // Align text vertically if needed
  } as ViewStyle,
  iconImage: {
    width: 45,
    height: 45,
    borderRadius: 22.5, // Make it circular
  } as ImageStyle,
})

// --- Example Usage (How you might use it) ---

/*
import { StoreWidgetCard } from './StoreWidgetCard'; // Adjust path

const MyScreen = () => {
  return (
    <View style={{ padding: 20 }}>
      <StoreWidgetCard
        storeName="Chantal's Fashion Boutique"
        storeType="Retail Clothing"
        iconName="hanger" // Example icon name
        iconColor={brandColors.coral} // Use coral for this icon
        metricLabel="Status"
        metricValue="Online"
        metricValueColor="#28a745" // Green for Online status
        onPress={() => console.log('Navigate to Store Chantal')}
        style={{ marginBottom: 15 }} // Add margin below
      />

      <StoreWidgetCard
        storeName="Patisserie Deluxe"
        storeType="Bakery & Cafe"
        iconName="cupcake" // Example icon name
        metricLabel="Orders Today"
        metricValue={15}
        metricValueColor={brandColors.brightTeal} // Use bright teal for order count
        onPress={() => console.log('Navigate to Store Patisserie')}
      />

       <StoreWidgetCard
        storeName="Hardware Hub"
        storeType="Tools & Supplies"
        iconName="toolbox-outline" // Example icon name
        style={{ marginTop: 15 }} // Add margin above
        // No metric, no onPress
      />
    </View>
  );
};
*/
