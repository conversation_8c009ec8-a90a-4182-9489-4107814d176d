/* eslint-disable react-native/no-inline-styles */
/* eslint-disable import/no-unresolved */
import { StyleProp, TouchableOpacity, View, ViewStyle, ScrollView } from "react-native"
import { colors } from "@/theme"
import { Icon, Text } from "."
import { useState } from "react"
import { FedhaCardComingSoonModal } from "./FedhaCardComingSoonModal"

export interface QuickActionsProps {
  navigation: any
  style?: StyleProp<ViewStyle>
}

/**
 * Describe your component here
 */

export const QuickActions = (props: QuickActionsProps) => {
  const { style, navigation } = props
  const [showFedhaCardModal, setShowFedhaCardModal] = useState(false)
  // const { themed } = useAppTheme()

  const handlePreOrder = () => {
    // Handle pre-order logic here
    console.log("Pre-order FedhaCard")
    setShowFedhaCardModal(false)
  }

  return (
    <>
      <View style={[$quickActionsCard, style]}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={$scrollContent}
        >
          <TouchableOpacity style={$ActinBtn} onPress={() => navigation.navigate("Pay")}>
            <Icon
              icon={"paym"}
              color={colors.palette.neutral900}
              size={30}
              containerStyle={$ActionIcon}
            />
            <Text
              preset="subheading"
              style={{
                fontSize: 15,
                color: colors.palette.neutral900,
                fontWeight: "700",
                marginTop: 6,
              }}
            >
              Payer
            </Text>
          </TouchableOpacity>
          <TouchableOpacity style={$ActinBtn} onPress={() => navigation.navigate("Transfer")}>
            <Icon
              icon={"transfer"}
              color={colors.palette.neutral900}
              size={30}
              containerStyle={$ActionIcon}
            />
            <Text
              preset="subheading"
              style={{
                fontSize: 15,
                color: colors.palette.neutral900,
                fontWeight: "700",
                marginTop: 6,
              }}
            >
              Transfert
            </Text>
          </TouchableOpacity>
          <TouchableOpacity style={$ActinBtn} onPress={() => setShowFedhaCardModal(true)}>
            <Icon
              icon={"credidcard"}
              color={colors.palette.neutral900}
              size={30}
              containerStyle={$ActionIcon}
            />
            <Text
              preset="subheading"
              style={{
                fontSize: 15,
                color: colors.palette.neutral900,
                fontWeight: "700",
                marginTop: 6,
              }}
            >
              Cartes
            </Text>
          </TouchableOpacity>
          <TouchableOpacity style={$ActinBtn} onPress={() => navigation.navigate("Withdraw")}>
            <Icon
              icon={"withdrawal"}
              color={colors.palette.neutral900}
              size={30}
              containerStyle={$ActionIcon}
            />
            <Text
              preset="subheading"
              style={{
                fontSize: 15,
                color: colors.palette.neutral900,
                fontWeight: "700",
                marginTop: 6,
              }}
            >
              Retirer
            </Text>
          </TouchableOpacity>
        </ScrollView>
      </View>

      <FedhaCardComingSoonModal
        isVisible={showFedhaCardModal}
        onClose={() => setShowFedhaCardModal(false)}
        onPreOrder={handlePreOrder}
      />
    </>
  )
}

const $quickActionsCard: ViewStyle = {
  backgroundColor: colors.palette.neutral200,
  // borderRadius: 24,
  // marginHorizontal: 16,
  // marginTop: -32,
  // paddingVertical: 10,
  // paddingHorizontal: 8,
  // shadowColor: "#000",
  // shadowOffset: { width: 0, height: 2 },
  // shadowOpacity: 0.08,
  // shadowRadius: 8,
  // elevation: 3,
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "center",
}

const $scrollContent: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "flex-start",
}

const $ActinBtn: ViewStyle = {
  alignItems: "center",
  flex: 1,
  padding: 10,
  marginHorizontal: 8,
  minWidth: 80,
}

const $ActionIcon: ViewStyle = {}
