import { useState } from "react"
import {
  StyleProp,
  TextStyle,
  View,
  ViewStyle,
  TouchableOpacity,
  Image,
  Modal,
  ImageStyle,
} from "react-native"
import { useAppTheme } from "@/utils/useAppTheme"
import { colors, spacing, type ThemedStyle } from "@/theme"
import { CartWidget, Text, Icon } from "."
import { useStores } from "@/store"
// import { transparent } from "react-native-paper/lib/typescript/styles/themes/v2/colors"
// import { useStores } from "@/model/rootStore" // Uncomment if needed later

export interface BheaderProps {
  navigation: any
  style?: StyleProp<ViewStyle>
  businessName?: string
  businessDescription?: string
  businessLogo?: any
}

/**
 * Business header component with logo, name and cart widget
 */
export const Bheader = (props: BheaderProps) => {
  // We can use user data later if needed
  const {
    // auth: { user },
    business: { businesses },
  } = useStores()

  const {
    style,
    // navigation is passed but not used directly in this component
    navigation,
    businessName,
    businessDescription,
    businessLogo: propBusinessLogo,
  } = props

  // Default business logo
  const defaultBusinessLogo = require("../../assets/images/fedhaSafeLogo.png")

  // Use the business from props or from the store
  const name =
    businessName || (businesses && businesses.length > 0 ? businesses[0].name : "Fedha Business")
  const description =
    businessDescription ||
    (businesses && businesses.length > 0 ? businesses[0].description : "Your business description")

  // Use the logo from props, or if it's null/undefined, use the default logo
  const businessLogo = propBusinessLogo || defaultBusinessLogo

  const $styles = [$container, style]
  const { themed } = useAppTheme()

  // State for modal visibility
  const [modalVisible, setModalVisible] = useState(false)

  // console.log('f', businesses)
  return (
    <View style={$styles}>
      {/* Business Logo and Name */}
      <TouchableOpacity
        style={$businessContainer}
        onPress={() => setModalVisible(true)}
        activeOpacity={0.7}
      >
        {/* <View style={$logoContainer}>
          <Image source={businessLogo} style={$logo} resizeMode="contain" />
        </View> */}
        {businessLogo && (
          <View style={$logoContainer}>
            <Image source={businessLogo} style={$iconImage} resizeMode="cover" />
          </View>
        )}
        <Text style={themed($businessName)}>{name}</Text>
      </TouchableOpacity>

      {/* Cart Widget */}
      <CartWidget navigation={navigation} />

      {/* Business Details Modal */}
      <Modal
        animationType="fade"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <TouchableOpacity
          style={$modalOverlay}
          activeOpacity={1}
          onPress={() => setModalVisible(false)}
        >
          <View style={$modalContent}>
            <TouchableOpacity style={$closeButton} onPress={() => setModalVisible(false)}>
              <Icon icon="x" size={24} color={colors.palette.neutral800} />
            </TouchableOpacity>

            <Image source={businessLogo} style={$modalLogo} resizeMode="contain" />

            <Text style={$modalBusinessName}>{name}</Text>

            <Text style={$modalDescription}>{description}</Text>
          </View>
        </TouchableOpacity>
      </Modal>
    </View>
  )
}

const $container: ViewStyle = {
  justifyContent: "space-between",
  flexDirection: "row",
  alignItems: "center",
  paddingVertical: 3,
  borderBottomWidth: 1,
  borderBottomColor: colors.palette.neutral300,
  // paddingTop: spacing.lg,
  paddingHorizontal: spacing.md,
}
const $iconImage: ImageStyle = {
  width: 40,
  height: 40,
  borderRadius: 22.5,
}

// Business container styles
const $businessContainer: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
}

const $logoContainer: ViewStyle = {
  // width: 40,
  // height: 40,
  // borderRadius: 20,
  // backgroundColor: colors.palette.neutral100,
  justifyContent: "center",
  alignItems: "center",
  // marginRight: spacing.xs,
  overflow: "hidden",
  // borderWidth: 1,
  // borderColor: colors.palette.neutral300,
}

// const $logo: ImageStyle = {
//   width: 40,
//   height: 40,
// }

const $businessName: ThemedStyle<TextStyle> = ({ colors, typography }) => ({
  fontFamily: typography.primary.medium,
  fontSize: 18,
  color: colors.palette.neutral900,
  marginLeft: spacing.xs,
})

// Modal styles
const $modalOverlay: ViewStyle = {
  flex: 1,
  backgroundColor: "rgba(0, 0, 0, 0.5)",
  justifyContent: "center",
  alignItems: "center",
  padding: spacing.lg,
}

const $modalContent: ViewStyle = {
  width: "90%",
  backgroundColor: colors.background,
  borderRadius: 12,
  padding: spacing.lg,
  alignItems: "center",
  shadowColor: "#000",
  shadowOffset: { width: 0, height: 2 },
  shadowOpacity: 0.25,
  shadowRadius: 3.84,
  elevation: 5,
}

const $closeButton: ViewStyle = {
  position: "absolute",
  top: spacing.sm,
  right: spacing.sm,
  zIndex: 1,
  padding: spacing.xs,
}

const $modalLogo: ImageStyle = {
  width: 100,
  height: 100,
  marginBottom: spacing.md,
}

const $modalBusinessName: TextStyle = {
  fontSize: 24,
  fontWeight: "bold",
  color: colors.palette.primary600,
  marginBottom: spacing.sm,
  textAlign: "center",
}

const $modalDescription: TextStyle = {
  fontSize: 16,
  color: colors.palette.neutral700,
  textAlign: "center",
  lineHeight: 22,
}
