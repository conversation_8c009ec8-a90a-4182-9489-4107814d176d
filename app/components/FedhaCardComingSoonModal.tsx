/* eslint-disable react-native/no-inline-styles */
/* eslint-disable react-native/no-color-literals */
import { useEffect, useRef, useState } from "react"
import { View, Modal, TouchableOpacity, Text, Animated, StyleSheet, Easing } from "react-native"
import { colors, spacing } from "@/theme"
import { Icon } from "."

interface FedhaCardComingSoonModalProps {
  isVisible: boolean
  onClose: () => void
  onPreOrder: () => void
}

export const FedhaCardComingSoonModal: React.FC<FedhaCardComingSoonModalProps> = ({
  isVisible,
  onClose,
  onPreOrder,
}) => {
  const rotateAnim = useRef(new Animated.Value(0)).current
  const scaleAnim = useRef(new Animated.Value(0.8)).current
  const fadeAnim = useRef(new Animated.Value(0)).current
  const [showConfirmation, setShowConfirmation] = useState(false)

  useEffect(() => {
    if (isVisible) {
      // Start animations when modal opens
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 400,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.loop(
          Animated.timing(rotateAnim, {
            toValue: 1,
            duration: 6000, // slower and smoother
            easing: Easing.inOut(Easing.ease),
            useNativeDriver: true,
          }),
        ),
      ]).start()
    } else {
      // Reset animations when modal closes
      fadeAnim.setValue(0)
      scaleAnim.setValue(0.8)
      rotateAnim.setValue(0)
    }
  }, [isVisible, fadeAnim, scaleAnim, rotateAnim])

  // Rotate on Y axis for left-to-right effect
  const rotateY = rotateAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ["0deg", "360deg"],
  })

  const handlePreOrderClick = () => setShowConfirmation(true)
  const handleConfirmationClose = () => {
    setShowConfirmation(false)
    onPreOrder()
  }

  return (
    <Modal visible={isVisible} animationType="fade" transparent={true}>
      <Animated.View style={[styles.overlay, { opacity: fadeAnim }]}>
        <View style={styles.container}>
          {/* Card Animation */}
          <View style={styles.cardContainer}>
            <Animated.View
              style={[
                styles.card,
                {
                  backgroundColor: colors.palette.primary200,
                  transform: [{ perspective: 800 }, { rotateY }, { scale: scaleAnim }],
                  shadowColor: colors.palette.primary300,
                },
              ]}
            >
              {/* Card Design */}
              <View style={styles.cardContent}>
                <View style={styles.cardHeader}>
                  <Text style={[styles.cardBrand, { color: colors.palette.neutral900 }]}>
                    FEDHA
                  </Text>
                  <View style={[styles.chip, { backgroundColor: colors.palette.accent200 }]} />
                </View>
                <View style={styles.cardBody}>
                  <View
                    style={[styles.contactlessIcon, { backgroundColor: colors.palette.primary100 }]}
                  >
                    <Icon icon="transfer" size={20} color={colors.palette.primary300} />
                  </View>
                </View>
                <View style={styles.cardFooter}>
                  <Text style={[styles.cardType, { color: colors.palette.primary300 }]}>DEBIT</Text>
                </View>
              </View>
            </Animated.View>
          </View>

          {/* Content */}
          <View style={styles.content}>
            {!showConfirmation ? (
              <>
                <Text style={styles.title}>FedhaCard</Text>
                <Text style={styles.subtitle}>Bientôt disponible !</Text>
                <Text style={styles.description}>
                  Préparez-vous pour l&lsquo;avenir des paiements ! La FedhaCard sera directement
                  liée à votre portefeuille FedhaPochi, pour des transactions fluides et des
                  avantages exclusifs.
                </Text>
                <View style={styles.buttonContainer}>
                  <TouchableOpacity style={styles.preOrderButton} onPress={handlePreOrderClick}>
                    <Text style={styles.preOrderButtonText}>
                      Précommander pour un accès exclusif
                    </Text>
                  </TouchableOpacity>
                  <TouchableOpacity style={styles.okayButton} onPress={onClose}>
                    <Text style={styles.okayButtonText}>Fermer</Text>
                  </TouchableOpacity>
                </View>
              </>
            ) : (
              <>
                <Text style={styles.title}>Parfait !</Text>
                <Text style={styles.subtitle}>Vous êtes sur la liste d&lsquo;attente</Text>
                <Text style={styles.description}>
                  Merci pour votre intérêt ! Vous avez été ajouté à notre liste d&lsquo;attente
                  exclusive pour la FedhaCard. Nous vous contacterons dès que les cartes seront
                  disponibles.
                </Text>
                <View style={styles.buttonContainer}>
                  <TouchableOpacity style={styles.preOrderButton} onPress={handleConfirmationClose}>
                    <Text style={styles.preOrderButtonText}>Parfait</Text>
                  </TouchableOpacity>
                </View>
              </>
            )}
          </View>
        </View>
      </Animated.View>
    </Modal>
  )
}

const styles = StyleSheet.create({
  buttonContainer: {
    gap: spacing.md,
    width: "100%",
  },
  card: {
    backgroundColor: colors.palette.primary300,
    borderRadius: 16,
    elevation: 8,
    height: 180,
    padding: spacing.md,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    width: 280,
  },
  cardBody: {
    alignItems: "flex-end",
    flex: 1,
    justifyContent: "center",
  },
  cardBrand: {
    color: colors.palette.neutral100,
    fontSize: 24,
    fontWeight: "bold",
    letterSpacing: 2,
  },
  cardContainer: {
    alignItems: "center",
    marginBottom: spacing.xxl,
  },
  cardContent: {
    flex: 1,
    justifyContent: "space-between",
  },
  cardFooter: {
    alignItems: "flex-start",
  },
  cardHeader: {
    alignItems: "center",
    flexDirection: "row",
    justifyContent: "space-between",
  },
  cardType: {
    color: colors.palette.neutral100,
    fontSize: 14,
    fontWeight: "600",
    letterSpacing: 1,
  },
  chip: {
    backgroundColor: colors.palette.accent300,
    borderRadius: 4,
    height: 30,
    width: 40,
  },
  contactlessIcon: {
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    borderRadius: 20,
    height: 40,
    justifyContent: "center",
    width: 40,
  },
  container: {
    alignItems: "center",
    flex: 1,
    justifyContent: "center",
    paddingHorizontal: spacing.lg,
  },
  content: {
    alignItems: "center",
    maxWidth: 320,
  },
  description: {
    color: colors.palette.neutral200,
    fontSize: 16,
    lineHeight: 24,
    marginBottom: spacing.xxl,
    textAlign: "center",
  },
  okayButton: {
    alignItems: "center",
    backgroundColor: "transparent",
    borderColor: colors.palette.neutral400,
    borderRadius: 12,
    borderWidth: 1,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  okayButtonText: {
    color: colors.palette.neutral300,
    fontSize: 16,
    fontWeight: "600",
  },
  overlay: {
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.8)",
    flex: 1,
    justifyContent: "center",
  },
  preOrderButton: {
    alignItems: "center",
    backgroundColor: colors.palette.primary300,
    borderRadius: 12,
    elevation: 4,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
  },
  preOrderButtonText: {
    color: colors.palette.neutral100,
    fontSize: 16,
    fontWeight: "600",
  },
  subtitle: {
    color: colors.palette.primary200,
    fontSize: 20,
    fontWeight: "600",
    marginBottom: spacing.lg,
    textAlign: "center",
  },
  title: {
    color: colors.palette.neutral100,
    fontSize: 32,
    fontWeight: "bold",
    marginBottom: spacing.xs,
    textAlign: "center",
  },
})
