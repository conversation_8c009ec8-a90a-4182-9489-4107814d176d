import { useState } from "react"
import { View, TouchableOpacity, ViewStyle, TextStyle, Modal, TextInput } from "react-native"
import { Text, Icon } from "."
import { colors, spacing } from "@/theme"

export interface SavedDevice {
  id: string
  type: "electricity" | "water" | "internet" | "other"
  name: string
  number: string
  provider: string
  isActive: boolean
}

interface SavedDevicesCardProps {
  device?: SavedDevice
  onPress?: (device: SavedDevice) => void
  onAddNew?: () => void
  onModify?: (device: SavedDevice) => void
  onDelete?: (device: SavedDevice) => void
  isAddCard?: boolean
  style?: ViewStyle
}

interface DeviceFormData {
  name: string
  number: string
  type: SavedDevice["type"]
}

export const SavedDevicesCard: React.FC<SavedDevicesCardProps> = ({
  device,
  onPress,
  onAddNew,
  onModify,
  onDelete,
  isAddCard = false,
  style,
}) => {
  const [showForm, setShowForm] = useState(false)
  const [formData, setFormData] = useState<DeviceFormData>({
    name: "",
    number: "",
    type: "electricity",
  })

  const getDeviceTypeIcon = (type: SavedDevice["type"]) => {
    switch (type) {
      case "electricity":
        return "lightbulb"
      case "water":
        return "water"
      case "internet":
        return "transfer"
      default:
        return "utilities"
    }
  }

  const getDeviceTypeColor = (type: SavedDevice["type"]) => {
    switch (type) {
      case "electricity":
        return colors.palette.accent500
      case "water":
        return colors.palette.primary500
      case "internet":
        return colors.palette.secondary500
      default:
        return colors.palette.neutral500
    }
  }

  const getDeviceTypeText = (type: SavedDevice["type"]) => {
    switch (type) {
      case "electricity":
        return "Électricité"
      case "water":
        return "Eau"
      case "internet":
        return "Internet"
      default:
        return "Autre"
    }
  }

  const handleAddCardPress = () => {
    setFormData({ name: "", number: "", type: "electricity" })
    setShowForm(true)
  }

  const handleModifyPress = () => {
    if (device) {
      setFormData({
        name: device.name,
        number: device.number,
        type: device.type,
      })
      setShowForm(true)
    }
  }

  const handleFormSubmit = () => {
    if (formData.name.trim() && formData.number.trim()) {
      if (device) {
        // Modify existing device
        onModify?.({
          ...device,
          name: formData.name.trim(),
          number: formData.number.trim(),
          type: formData.type,
        })
      } else {
        // Add new device
        const newDevice: SavedDevice = {
          id: Date.now().toString(),
          name: formData.name.trim(),
          number: formData.number.trim(),
          type: formData.type,
          provider: "Provider", // This would be set based on type
          isActive: true,
        }
        onAddNew?.()
        console.log("New device:", newDevice)
      }
      setShowForm(false)
    }
  }

  const handleFormCancel = () => {
    setShowForm(false)
  }

  if (isAddCard) {
    return (
      <>
        <TouchableOpacity
          style={[$addCard, style]}
          onPress={handleAddCardPress}
          activeOpacity={0.8}
        >
          <View style={$addCardContent}>
            <View style={$addIconContainer}>
              <Icon icon="plus" size={24} color={colors.palette.primary500} />
            </View>
            <Text style={$addCardTitle}>Ajouter un compteur</Text>
            <Text style={$addCardSubtitle}>Connectez un nouveau compteur</Text>
          </View>
        </TouchableOpacity>

        {/* Add Device Form Modal */}
        <DeviceFormModal
          isVisible={showForm}
          formData={formData}
          setFormData={setFormData}
          onSubmit={handleFormSubmit}
          onCancel={handleFormCancel}
          title="Ajouter un compteur"
        />
      </>
    )
  }

  if (!device) return null

  return (
    <>
      <TouchableOpacity
        style={[$card, style]}
        onPress={() => onPress?.(device)}
        activeOpacity={0.8}
      >
        <View style={$cardHeader}>
          <View style={$deviceInfo}>
            <View style={$nameTypeRow}>
              <Text style={$deviceName}>{device.name}</Text>
              <View style={[$typeBadge, { backgroundColor: getDeviceTypeColor(device.type) }]}>
                <Icon
                  icon={getDeviceTypeIcon(device.type)}
                  size={12}
                  color={colors.palette.neutral100}
                />
                <Text style={$typeText}>{getDeviceTypeText(device.type)}</Text>
              </View>
            </View>
            <Text style={$deviceNumber}>{device.number}</Text>
          </View>
        </View>

        <View style={$cardActions}>
          <TouchableOpacity style={$actionButton} onPress={handleModifyPress}>
            <Icon icon="edit" size={16} color={colors.palette.primary500} />
            <Text style={$actionText}>Modifier</Text>
          </TouchableOpacity>
          <TouchableOpacity style={$actionButton} onPress={() => onDelete?.(device)}>
            <Icon icon="delete" size={16} color={colors.palette.accent400} />
            <Text style={$actionText}>Supprimer</Text>
          </TouchableOpacity>
        </View>
      </TouchableOpacity>

      {/* Modify Device Form Modal */}
      <DeviceFormModal
        isVisible={showForm}
        formData={formData}
        setFormData={setFormData}
        onSubmit={handleFormSubmit}
        onCancel={handleFormCancel}
        title="Modifier le compteur"
      />
    </>
  )
}

// Device Form Modal Component
interface DeviceFormModalProps {
  isVisible: boolean
  formData: DeviceFormData
  setFormData: (data: DeviceFormData) => void
  onSubmit: () => void
  onCancel: () => void
  title: string
}

const DeviceFormModal: React.FC<DeviceFormModalProps> = ({
  isVisible,
  formData,
  setFormData,
  onSubmit,
  onCancel,
  title,
}) => {
  const deviceTypes: { value: SavedDevice["type"]; label: string; icon: string }[] = [
    { value: "electricity", label: "Électricité", icon: "lightbulb" },
    { value: "water", label: "Eau", icon: "water" },
    { value: "internet", label: "Internet", icon: "transfer" },
    { value: "other", label: "Autre", icon: "utilities" },
  ]

  return (
    <Modal visible={isVisible} animationType="slide" transparent={true}>
      <View style={$modalOverlay}>
        <View style={$modalContainer}>
          <View style={$modalHeader}>
            <Text style={$modalTitle}>{title}</Text>
            <TouchableOpacity onPress={onCancel} style={$closeButton}>
              <Icon icon="x" size={20} color={colors.palette.neutral600} />
            </TouchableOpacity>
          </View>

          <View style={$formContainer}>
            <View style={$formField}>
              <Text style={$formLabel}>Nom du compteur</Text>
              <TextInput
                style={$formInput}
                value={formData.name}
                onChangeText={(text) => setFormData({ ...formData, name: text })}
                placeholder="Ex: Compteur Principal"
                placeholderTextColor={colors.palette.neutral400}
              />
            </View>

            <View style={$formField}>
              <Text style={$formLabel}>Numéro du compteur</Text>
              <TextInput
                style={$formInput}
                value={formData.number}
                onChangeText={(text) => setFormData({ ...formData, number: text })}
                placeholder="Ex: 123456789"
                placeholderTextColor={colors.palette.neutral400}
              />
            </View>

            <View style={$formField}>
              <Text style={$formLabel}>Type de compteur</Text>
              <View style={$typeSelector}>
                {deviceTypes.map((type) => (
                  <TouchableOpacity
                    key={type.value}
                    style={[$typeOption, formData.type === type.value && $typeOptionSelected]}
                    onPress={() => setFormData({ ...formData, type: type.value })}
                  >
                    <Icon
                      icon={type.icon as any}
                      size={16}
                      color={
                        formData.type === type.value
                          ? colors.palette.neutral100
                          : colors.palette.neutral600
                      }
                    />
                    <Text
                      style={[
                        $typeOptionText,
                        formData.type === type.value && $typeOptionTextSelected,
                      ]}
                    >
                      {type.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </View>

          <View style={$modalActions}>
            <TouchableOpacity style={$cancelButton} onPress={onCancel}>
              <Text style={$cancelButtonText}>Annuler</Text>
            </TouchableOpacity>
            <TouchableOpacity style={$submitButton} onPress={onSubmit}>
              <Text style={$submitButtonText}>Enregistrer</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  )
}

const $card: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  borderRadius: 16,
  padding: spacing.md,
  marginBottom: spacing.sm,
  // Shadow for iOS
  shadowColor: colors.palette.neutral500,
  shadowOffset: { width: 0, height: 4 },
  shadowOpacity: 0.1,
  shadowRadius: 8,
  // Shadow for Android
  elevation: 4,
  borderWidth: 1,
  borderColor: colors.palette.neutral200,
}

const $addCard: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  borderRadius: 16,
  padding: spacing.lg,
  marginBottom: spacing.sm,
  borderWidth: 2,
  borderColor: colors.palette.primary200,
  borderStyle: "dashed",
  alignItems: "center",
  justifyContent: "center",
}

const $addCardContent: ViewStyle = {
  alignItems: "center",
}

const $addIconContainer: ViewStyle = {
  width: 48,
  height: 48,
  borderRadius: 24,
  backgroundColor: colors.palette.primary100,
  justifyContent: "center",
  alignItems: "center",
  marginBottom: spacing.sm,
}

const $addCardTitle: TextStyle = {
  fontSize: 16,
  fontWeight: "bold",
  color: colors.palette.primary500,
  marginBottom: spacing.xs,
}

const $addCardSubtitle: TextStyle = {
  fontSize: 14,
  color: colors.palette.neutral600,
  textAlign: "center",
}

const $cardHeader: ViewStyle = {
  marginBottom: spacing.md,
}

const $deviceInfo: ViewStyle = {
  flex: 1,
}

const $nameTypeRow: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "space-between",
  marginBottom: spacing.xs,
}

const $deviceName: TextStyle = {
  fontSize: 16,
  fontWeight: "bold",
  color: colors.palette.neutral900,
  flex: 1,
}

const $typeBadge: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  paddingHorizontal: spacing.sm,
  paddingVertical: spacing.xs,
  borderRadius: 8,
  gap: spacing.xs,
}

const $typeText: TextStyle = {
  fontSize: 12,
  fontWeight: "bold",
  color: colors.palette.neutral100,
}

const $deviceNumber: TextStyle = {
  fontSize: 18,
  fontWeight: "bold",
  color: colors.palette.neutral900,
  textAlign: "center",
  marginTop: spacing.sm,
}

const $cardActions: ViewStyle = {
  flexDirection: "row",
  gap: spacing.sm,
}

const $actionButton: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  paddingHorizontal: spacing.sm,
  paddingVertical: spacing.xs,
  borderRadius: 8,
  backgroundColor: colors.palette.neutral200,
  gap: spacing.xs,
  flex: 1,
  justifyContent: "center",
}

const $actionText: TextStyle = {
  fontSize: 12,
  fontWeight: "500",
  color: colors.palette.neutral700,
}

// Modal Styles
const $modalOverlay: ViewStyle = {
  flex: 1,
  backgroundColor: colors.palette.overlay50,
  justifyContent: "center",
  alignItems: "center",
}

const $modalContainer: ViewStyle = {
  backgroundColor: colors.palette.neutral100,
  borderRadius: 20,
  margin: spacing.lg,
  maxWidth: 400,
  width: "100%",
  // Shadow for iOS
  shadowColor: colors.palette.neutral900,
  shadowOffset: { width: 0, height: 10 },
  shadowOpacity: 0.3,
  shadowRadius: 20,
  // Shadow for Android
  elevation: 10,
}

const $modalHeader: ViewStyle = {
  flexDirection: "row",
  justifyContent: "space-between",
  alignItems: "center",
  padding: spacing.lg,
  borderBottomWidth: 1,
  borderBottomColor: colors.palette.neutral200,
}

const $modalTitle: TextStyle = {
  fontSize: 18,
  fontWeight: "bold",
  color: colors.palette.neutral900,
}

const $closeButton: ViewStyle = {
  padding: spacing.xs,
}

const $formContainer: ViewStyle = {
  padding: spacing.lg,
}

const $formField: ViewStyle = {
  marginBottom: spacing.lg,
}

const $formLabel: TextStyle = {
  fontSize: 14,
  fontWeight: "600",
  color: colors.palette.neutral900,
  marginBottom: spacing.xs,
}

const $formInput: TextStyle = {
  borderWidth: 1,
  borderColor: colors.palette.neutral300,
  borderRadius: 8,
  padding: spacing.sm,
  fontSize: 16,
  color: colors.palette.neutral900,
  backgroundColor: colors.palette.neutral100,
}

const $typeSelector: ViewStyle = {
  flexDirection: "row",
  flexWrap: "wrap",
  gap: spacing.xs,
}

const $typeOption: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  paddingHorizontal: spacing.sm,
  paddingVertical: spacing.xs,
  borderRadius: 8,
  borderWidth: 1,
  borderColor: colors.palette.neutral300,
  backgroundColor: colors.palette.neutral100,
  gap: spacing.xs,
  flex: 1,
  minWidth: "45%",
  justifyContent: "center",
}

const $typeOptionSelected: ViewStyle = {
  backgroundColor: colors.palette.primary500,
  borderColor: colors.palette.primary500,
}

const $typeOptionText: TextStyle = {
  fontSize: 12,
  fontWeight: "500",
  color: colors.palette.neutral600,
}

const $typeOptionTextSelected: TextStyle = {
  color: colors.palette.neutral100,
}

const $modalActions: ViewStyle = {
  flexDirection: "row",
  padding: spacing.lg,
  gap: spacing.sm,
  borderTopWidth: 1,
  borderTopColor: colors.palette.neutral200,
}

const $cancelButton: ViewStyle = {
  flex: 1,
  paddingVertical: spacing.sm,
  borderRadius: 8,
  borderWidth: 1,
  borderColor: colors.palette.neutral300,
  alignItems: "center",
}

const $cancelButtonText: TextStyle = {
  fontSize: 16,
  fontWeight: "600",
  color: colors.palette.neutral700,
}

const $submitButton: ViewStyle = {
  flex: 1,
  paddingVertical: spacing.sm,
  borderRadius: 8,
  backgroundColor: colors.palette.primary500,
  alignItems: "center",
}

const $submitButtonText: TextStyle = {
  fontSize: 16,
  fontWeight: "600",
  color: colors.palette.neutral100,
}
