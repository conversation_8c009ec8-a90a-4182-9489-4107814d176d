/* eslint-disable no-restricted-imports */
/* eslint-disable import/no-unresolved */
/**
 * NotificationContext - <PERSON>les push notification registration and management
 *
 * FIXED: Infinite loop issue where push token was being registered repeatedly
 * - Removed expoPushToken from useEffect dependencies to prevent re-triggering
 * - Added storage-based tracking to prevent duplicate registrations
 * - Added registration state to prevent concurrent registration attempts
 * - Improved error handling to gracefully handle 404 errors from backend
 */
import React, { createContext, useContext, useState, useEffect, useRef, ReactNode } from "react"
import * as Notifications from "expo-notifications"
// import { Subscription } from "expo-modules-core"
import { registerForPushNotificationsAsync } from "@/utils/registerForPushNotificationsAsync"
import { EventSubscription } from "expo-notifications"
import { useAuthStore } from "@/store/AuthenticationStore"
import { storage } from "@/utils/storage"
// import { Platform } from "react-native"

interface NotificationContextType {
  expoPushToken: string | null
  notification: Notifications.Notification | null
  error: Error | null
  sendTestNotification: () => Promise<void>
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined)

export const useNotification = () => {
  const context = useContext(NotificationContext)
  if (context === undefined) {
    throw new Error("useNotification must be used within a NotificationProvider")
  }
  return context
}

interface NotificationProviderProps {
  children: ReactNode
}

// Configure notification handler with custom sound
const configureNotifications = () => {
  Notifications.setNotificationHandler({
    handleNotification: async () => ({
      shouldShowAlert: true,
      shouldPlaySound: true,
      shouldSetBadge: false,
      // Use custom sound for notifications
      sound: "notificationsound.wav",
    }),
  })
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  // const registerPushToken = usePushNotificationStore((state) => state.registerPushToken)
  const [error, setError] = useState<Error | null>(null)
  const [notification, setNotification] = useState<Notifications.Notification | null>(null)
  const [expoPushToken, setExpoPushToken] = useState<string | null>(null)
  const [isRegistering, setIsRegistering] = useState(false)

  // Get authentication state from the auth store
  const isAuthenticated = useAuthStore((state) => state.isAuthenticated)
  const accessToken = useAuthStore((state) => state.accessToken)

  const notificationListener = useRef<EventSubscription>()
  const responseListener = useRef<EventSubscription>()

  // Configure notifications with custom sound when component mounts
  useEffect(() => {
    configureNotifications()

    // Clear any previously stored tokens on app start to ensure fresh registration
    // This prevents issues with stale tokens from previous sessions
    storage.delete("registeredPushToken")
    storage.delete("currentPushToken")
    console.log("🔔 Cleared stored push tokens on app start")
  }, [])

  // Set up notification listeners
  useEffect(() => {
    notificationListener.current = Notifications.addNotificationReceivedListener((notification) => {
      console.log("🔔 Notification Received: ", notification)
      setNotification(notification)
    })

    responseListener.current = Notifications.addNotificationResponseReceivedListener((response) => {
      console.log("🔔 Notification Response: ", response)
    })

    return () => {
      if (notificationListener.current) {
        Notifications.removeNotificationSubscription(notificationListener.current)
      }
      if (responseListener.current) {
        Notifications.removeNotificationSubscription(responseListener.current)
      }
    }
  }, [])

  // Function to send a test notification with custom sound
  const sendTestNotification = async () => {
    await Notifications.scheduleNotificationAsync({
      content: {
        title: "Test Notification",
        body: "This is a test notification with custom sound",
        sound: "notificationsound.wav",
        data: { data: "Test data" },
      },
      trigger: null, // Immediate notification
    })
    console.log("🔔 Test notification scheduled")
  }

  // Register for push notifications only when user is authenticated and has a token
  useEffect(() => {
    const registerForPushNotifications = async () => {
      // Only proceed if user is authenticated and has a token
      if (isAuthenticated && accessToken && !isRegistering) {
        // Check if we've already registered a token for this session
        const registeredToken = storage.getString("registeredPushToken")
        const currentToken = storage.getString("currentPushToken")

        // If we already have a registered token and it matches the current token, skip registration
        if (registeredToken && currentToken && registeredToken === currentToken) {
          console.log("🔔 Push token already registered for this session")
          setExpoPushToken(currentToken)
          return
        }

        console.log("🔔 User is authenticated, registering for push notifications")
        setIsRegistering(true)

        try {
          // Pass true to register with backend since user is authenticated
          const token = await registerForPushNotificationsAsync(true)

          if (token) {
            setExpoPushToken(token)
            // Store the registered token to prevent re-registration
            storage.set("registeredPushToken", token)
            storage.set("currentPushToken", token)
            console.log("🔔 Push token registered and stored successfully")
          }
        } catch (err) {
          console.error("❌ Failed to register for push notifications:", err)
          setError(err instanceof Error ? err : new Error(String(err)))
        } finally {
          setIsRegistering(false)
        }
      } else if (!isAuthenticated) {
        console.log("🔔 User is not authenticated, skipping push notification registration")
        // Clear token if user logs out
        if (expoPushToken) {
          setExpoPushToken(null)
          // Clear stored tokens when user logs out
          storage.delete("registeredPushToken")
          storage.delete("currentPushToken")
          console.log("🔔 Cleared push notification tokens on logout")
        }
      }
    }

    registerForPushNotifications()
  }, [isAuthenticated, accessToken]) // Removed expoPushToken from dependencies

  return (
    <NotificationContext.Provider
      value={{ expoPushToken, notification, error, sendTestNotification }}
    >
      {children}
    </NotificationContext.Provider>
  )
}
