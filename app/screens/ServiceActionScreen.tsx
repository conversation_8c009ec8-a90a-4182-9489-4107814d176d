/* eslint-disable no-restricted-imports */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable import/no-unresolved */
import { FC, useState } from "react"
import { View, ViewStyle, TouchableOpacity, TextStyle, FlatList } from "react-native"
import { AppStackScreenProps } from "@/navigators"
import {
  Screen,
  Text,
  Header,
  Icon,
  BillsHistoryCard,
  SavedDevicesCard,
  type BillHistoryItem,
  type SavedDevice,
} from "@/components"
import { colors, spacing } from "@/theme"
import { useStores } from "@/store/rootStore"

interface ServiceActionScreenProps extends AppStackScreenProps<"ServiceAction"> {}

type TabType = "bills" | "devices"

export const ServiceActionScreen: FC<ServiceActionScreenProps> = ({ navigation }) => {
  const [activeTab, setActiveTab] = useState<TabType>("bills")

  const {
    services: { Services },
  } = useStores()

  // Dummy data for bills history
  const mockBills: BillHistoryItem[] = [
    {
      id: "1",
      type: "electricity",
      provider: "SNEL",
      amount: 25000,
      currency: "FC",
      status: "completed",
      date: "2024-01-15T10:30:00Z",
      meterNumber: "123456789",
      reference: "SNEL-2024-001",
    },
    {
      id: "2",
      type: "water",
      provider: "REGIDESO",
      amount: 15000,
      currency: "FC",
      status: "pending",
      date: "2024-01-14T14:20:00Z",
      meterNumber: "987654321",
      reference: "REG-2024-002",
    },
    {
      id: "3",
      type: "internet",
      provider: "Orange",
      amount: 45000,
      currency: "FC",
      status: "completed",
      date: "2024-01-13T09:15:00Z",
      reference: "ORANGE-2024-003",
    },
    {
      id: "4",
      type: "electricity",
      provider: "SNEL",
      amount: 18000,
      currency: "FC",
      status: "failed",
      date: "2024-01-12T16:45:00Z",
      meterNumber: "123456789",
      reference: "SNEL-2024-004",
    },
    {
      id: "5",
      type: "phone",
      provider: "Airtel",
      amount: 12000,
      currency: "FC",
      status: "completed",
      date: "2024-01-11T11:20:00Z",
      reference: "AIRTEL-2024-005",
    },
  ]

  // Dummy data for saved devices
  const mockDevices: SavedDevice[] = [
    {
      id: "1",
      type: "electricity",
      name: "Compteur Principal",
      number: "123456789",
      provider: "SNEL",
      isActive: true,
    },
    {
      id: "2",
      type: "water",
      name: "Compteur Eau",
      number: "987654321",
      provider: "REGIDESO",
      isActive: true,
    },
    {
      id: "3",
      type: "internet",
      name: "Modem Orange",
      number: "ORANGE-001",
      provider: "Orange",
      isActive: false,
    },
  ]

  const handleBillPress = (bill: BillHistoryItem) => {
    console.log("Bill pressed:", bill)
    // Navigate to bill details or payment screen
  }

  const handleDevicePress = (device: SavedDevice) => {
    console.log("Device pressed:", device)
    // Navigate to device details or payment screen
  }

  const handleAddDevice = () => {
    console.log("Add device pressed")
    // The form is now handled within the SavedDevicesCard component
  }

  const handleModifyDevice = (device: SavedDevice) => {
    console.log("Modify device:", device)
    // The form is now handled within the SavedDevicesCard component
  }

  const handleDeleteDevice = (device: SavedDevice) => {
    console.log("Delete device:", device)
    // Handle device deletion
  }

  const renderBillsTab = () => (
    <FlatList
      data={mockBills}
      keyExtractor={(item) => item.id}
      renderItem={({ item }) => <BillsHistoryCard bill={item} onPress={handleBillPress} />}
      contentContainerStyle={$listContainer}
      showsVerticalScrollIndicator={false}
    />
  )

  const renderDevicesTab = () => (
    <FlatList
      data={mockDevices}
      keyExtractor={(item) => item.id}
      renderItem={({ item }) => (
        <SavedDevicesCard
          device={item}
          onPress={handleDevicePress}
          onModify={handleModifyDevice}
          onDelete={handleDeleteDevice}
        />
      )}
      ListFooterComponent={() => <SavedDevicesCard isAddCard onAddNew={handleAddDevice} />}
      contentContainerStyle={$listContainer}
      showsVerticalScrollIndicator={false}
    />
  )

  return (
    <>
      <Header
        leftIcon="backicon"
        onLeftPress={navigation.goBack}
        title="Services"
        backgroundColor={colors.palette.neutral100}
      />

      <Screen style={$root} preset="fixed" statusBarStyle="dark">
        {/* Tab Navigation */}
        <View style={$tabContainer}>
          <TouchableOpacity
            style={[$tabButton, activeTab === "bills" && $activeTabButton]}
            onPress={() => setActiveTab("bills")}
          >
            <View style={$tabButtonContent}>
              <Icon
                icon="utilities"
                color={activeTab === "bills" ? colors.palette.neutral900 : colors.palette.accent300}
                size={24}
              />
              <Text style={[$tabText, activeTab === "bills" && $activeTabText]}>Mes Factures</Text>
            </View>
          </TouchableOpacity>

          <TouchableOpacity
            style={[$tabButton, activeTab === "devices" && $activeTabButton]}
            onPress={() => setActiveTab("devices")}
          >
            <View style={$tabButtonContent}>
              <Icon
                icon="cashpower"
                color={
                  activeTab === "devices" ? colors.palette.neutral900 : colors.palette.accent300
                }
                size={24}
              />
              <Text style={[$tabText, activeTab === "devices" && $activeTabText]}>
                Mes Compteurs
              </Text>
            </View>
          </TouchableOpacity>
        </View>

        {/* Tab Content */}
        {activeTab === "bills" ? renderBillsTab() : renderDevicesTab()}
      </Screen>
    </>
  )
}

const $root: ViewStyle = {
  flex: 1,
  backgroundColor: colors.palette.neutral100,
}

const $tabContainer: ViewStyle = {
  flexDirection: "row",
  paddingHorizontal: spacing.md,
  paddingVertical: spacing.sm,
  backgroundColor: colors.palette.neutral100,
  borderBottomWidth: 1,
  borderBottomColor: colors.palette.neutral200,
}

const $tabButton: ViewStyle = {
  flex: 1,
  paddingVertical: spacing.sm,
  borderBottomWidth: 2,
  borderBottomColor: colors.palette.neutral300,
}

const $activeTabButton: ViewStyle = {
  borderBottomColor: colors.palette.neutral900,
}

const $tabButtonContent: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  justifyContent: "center",
  gap: spacing.xs,
}

const $tabText: TextStyle = {
  fontSize: 14,
  color: colors.palette.neutral600,
}

const $activeTabText: TextStyle = {
  color: colors.palette.neutral900,
  fontWeight: "bold",
}

const $listContainer: ViewStyle = {
  paddingHorizontal: spacing.md,
  paddingBottom: spacing.lg,
}
