/* eslint-disable no-restricted-imports */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable react-native/no-inline-styles */
import { FC, useEffect, useMemo, useState, useCallback } from "react"
import { View, ViewStyle, ActivityIndicator, TouchableOpacity, Text } from "react-native"
import { AppStackScreenProps } from "@/navigators"
import {
  ActionIcon,
  BuyElectricity,
  MainHeader,
  ProgressReport,
  QuickActions,
  Screen,
  SimpleLoader,
  TestNotificationButton,
} from "@/components"
import { colors, spacing } from "@/theme"
import { useStores } from "@/store/rootStore"
import { usePostHog } from "posthog-react-native"
import { captureException, captureMessage } from "@/utils/sentryUtils"
import { DashboardFeature } from "@/utils/Menus"
import React from "react"

interface DashboardScreenProps extends AppStackScreenProps<"Dashboard"> {}

export const DashboardScreen: FC<DashboardScreenProps> = ({ navigation }) => {
  const {
    auth: { fetchUserData, user },
    fedhapochi: { fetchWallet },
    transactions: { fetchTransactions },
    notif: { getNotifications, notificationsList },
    beneficiary: { fetchBeneficiaries },
    elimu: { fetchCourses, courses },
    services: { fectchServices },
  } = useStores()

  const [messageNotifications, setMessageNotifications] = useState(1)
  const [isLoading, setIsLoading] = useState(true)
  const posthog = usePostHog()

  const loadAllData = useCallback(
    async (showLoading = true) => {
      if (showLoading) setIsLoading(true)

      try {
        const results = await Promise.allSettled([
          fetchUserData(),
          fetchWallet(),
          fectchServices(),
          getNotifications(),
          fetchBeneficiaries(),
          // fetchCourses(),
          fetchTransactions(),
          // fetchSNELHistory(),
        ])

        results.forEach((result, index) => {
          if (result.status === "rejected") {
            const endpoints = ["user data", "wallet", "beneficiaries", "transactions"]
            console.error(`Failed to fetch ${endpoints[index]}:`, result.reason)
          }
        })

        const hasCriticalFailure = results.some(
          (result, index) => result.status === "rejected" && [0, 1, 2].includes(index),
        )

        if (hasCriticalFailure) {
          throw new Error("Failed to load critical dashboard data")
        }
      } catch (error) {
        console.error("Dashboard data loading error:", error)
      } finally {
        setIsLoading(false)
      }
    },
    [
      fetchUserData,
      fetchWallet,
      getNotifications,
      fetchBeneficiaries,
      fectchServices,
      // fetchCourses,
      fetchTransactions,
      // fetchSNELHistory,
    ],
  )

  useEffect(() => {
    if (posthog) {
      posthog.capture("Fedha App loaded")
    }
    loadAllData()
  }, [posthog, loadAllData])

  const handleRefresh = useCallback(async () => {
    await loadAllData(false)
  }, [loadAllData])

  useEffect(() => {
    const refreshInterval = setInterval(handleRefresh, 30000)
    return () => clearInterval(refreshInterval)
  }, [handleRefresh])

  // const latestRequest = useMemo(() => {
  //   if (!Array.isArray(snelHistory)) {
  //     return null
  //   }

  //   try {
  //     const allRequests = snelHistory.flatMap((meter) => meter.requests)
  //     return allRequests.length > 0 ? allRequests[0] : null
  //   } catch (error) {
  //     if (__DEV__) {
  //       console.error("Error getting latest request:", error)
  //     }
  //     return null
  //   }
  // }, [snelHistory])

  const handleRequestPress = useCallback(() => {
    navigation.navigate("ServiceAction")
  }, [navigation])

  const handleServicePress = useCallback(
    (service: any) => {
      switch (service.serviceType) {
        case "Achetez":
          navigation.navigate("Buy")
          break
        case "Factures":
          navigation.navigate("ServiceAction")
          break
        case "Coupons":
          navigation.navigate("Coupons")
          break
        case "Message":
          navigation.navigate("Message")
          break
        case "Elimu":
          navigation.navigate("Elimu")
          break
        case "Informations":
          navigation.navigate("Info")
          break
        default:
          console.log("Service not implemented:", service.serviceType)
      }
    },
    [navigation],
  )

  if (isLoading) {
    return <SimpleLoader visible={true} />
  }
  return (
    <>
      <View style={$HeaderMain}>
        <MainHeader navigation={navigation} />
        <QuickActions navigation={navigation} />
      </View>

      <Screen style={$root} preset="scroll" statusBarStyle="dark">
        <View style={$ContentSections}>
          {DashboardFeature.map((item, index) => {
            let needAttention = null

            // if (item.serviceName === "Factures" && latestRequest) {
            //   needAttention = 1
            // }

            if (item.serviceName === "Message" && messageNotifications > 0) {
              needAttention = messageNotifications
            }

            return (
              <ActionIcon
                key={index}
                icon={item.iconName}
                title={item.serviceName}
                needAttention={needAttention}
                onPress={() => handleServicePress(item)}
              />
            )
          })}
        </View>
      </Screen>
      {/* <SimpleLoader visible={isLoading} /> */}
    </>
  )
}

const $root: ViewStyle = {
  // flex: 1,
  // paddingBottom: spacing.xl,
  // borderTopEndRadius: 20,
  // borderTopStartRadius: 20,
  paddingHorizontal: 15,
  backgroundColor: colors.palette.neutral100,
}

const $loadingContainer: ViewStyle = {
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
}

const $ContentSections: ViewStyle = {
  paddingTop: spacing.lg,
  flex: 1,
  flexDirection: "row",
  flexWrap: "wrap",
  backgroundColor: colors.palette.neutral100,
  // alignItems: "center",
  justifyContent: "flex-start",
  // gap: 4,
}

const $HeaderMain: ViewStyle = {
  paddingTop: spacing.xxl,
  // flex: 1,
  backgroundColor: colors.palette.neutral200,
  justifyContent: "flex-start",
}
