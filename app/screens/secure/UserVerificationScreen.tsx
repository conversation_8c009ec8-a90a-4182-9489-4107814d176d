/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable prettier/prettier */
/* eslint-disable no-restricted-imports */
/* eslint-disable react-native/no-inline-styles */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { FC, useEffect, useMemo, useState } from "react"
import { Linking, TextStyle, View, ViewStyle } from "react-native"
import { AppStackScreenProps } from "@/navigators"
import {
  Button,
  DocumentVerification,
  Header,
  Icon,
  Screen,
  Text,
  UserAdvanceInfo,
  UserProfileUpdate,
} from "@/components"
import { useStores } from "@/store/rootStore"
import { $Gstyles, colors, spacing, UserVerificationStyles } from "@/theme"
import { ProgressBar } from "react-native-paper"
import { useAppTheme } from "@/utils/useAppTheme"
import { getProgressDetails } from "@/utils/actions"
import React from "react"
// import { useNavigation } from "@react-navigation/native"

// Add this function at the top of your component

interface UserVerificationScreenProps extends AppStackScreenProps<"UserVerification"> {}

export const UserVerificationScreen: FC<UserVerificationScreenProps> = ({ navigation }) => {
  const {
    auth: { logout, isInfoVerified, isDocumentVerified, user, fetchUserData },
  } = useStores()

  // Add this useEffect at the top of your component
  useEffect(() => {
    const checkAndRedirect = async () => {
      // First ensure we have user data
      if (!user) {
        await fetchUserData()
        return
      }

      // Check all verification statuses
      const isFullyVerified =
        user.is_phone_verified &&
        user.is_documents_verified &&
        isInfoVerified &&
        user.PersonalInfo?.nationality && // Check basic personal info
        user.PersonalInfo?.date_de_naissance &&
        user.PersonalInfo?.addresse &&
        user.PersonalInfo?.ville_de_naissance &&
        user.PersonalInfo?.pays_des_residence &&
        user.PersonalInfo?.profession &&
        user.PersonalInfo?.etat_civil &&
        user.PersonalInfo?.ville_de_residence

      if (isFullyVerified) {
        // If everything is verified, redirect to FedhaLoader
        console.log("ver", isFullyVerified)
        navigation.reset({
          index: 0,
          routes: [{ name: "FedhaLoader" }],
        })
      }
    }

    // Run the check immediately and then every 5 seconds
    checkAndRedirect()
    const interval = setInterval(checkAndRedirect, 5000)

    // Cleanup interval on component unmount
    return () => clearInterval(interval)
  }, [user, isInfoVerified, navigation, fetchUserData])

  const [currentStep, setCurrentStep] = useState<"main" | "basic" | "advance" | "documents">("main")
  const { themed } = useAppTheme()
  const { button, header, progressBar, title, sectionHeader, serviceName, subHeading } =
    UserVerificationStyles
  const usrData = user?.PersonalInfo


  useEffect(() => {
    if (!user) {
      fetchUserData()
    }
  }, [fetchUserData, user])

  // Check if user has filled out their data
  const isBasicInfoComplete = useMemo(() => {
    return user?.email && user?.first_name && user?.last_name // Add other required fields if necessary
  }, [user])

  const isAdvanceInfoComplete = useMemo(() => {
    return (
      usrData?.nationality &&
      usrData?.date_de_naissance &&
      usrData?.addresse &&
      usrData?.ville_de_naissance &&
      usrData?.pays_des_residence &&
      usrData?.profession &&
      usrData?.etat_civil &&
      usrData?.ville_de_residence
    )
  }, [usrData])

  const isAllVerified = isBasicInfoComplete && isAdvanceInfoComplete

  console.log(isInfoVerified)

  const { progress, color } = getProgressDetails(user?.verification_status, user)

  const handleOpenVerification = () => {
    if (!isBasicInfoComplete) {
      setCurrentStep("basic")
    } else if (!isAdvanceInfoComplete) {
      setCurrentStep("advance")
    }
  }
  // console.log('deti:',user)

  if (currentStep === "basic") {
    return (
      <>
        <Header
          leftIcon={"backicon"}
          title="Verification Account"
          onLeftPress={() => setCurrentStep("main")}
        />
        <Screen
          style={$root}
          preset="scroll"
          safeAreaEdges={["bottom"]}
          StatusBarProps={{
            backgroundColor: colors.palette.neutral900,
          }}
        >
          <UserProfileUpdate
            onCompleted={() => {
              setCurrentStep("advance")
            }}
          />
        </Screen>
      </>
    )
  }

  if (currentStep === "advance") {
    return (
      <>
        <Header
          leftIcon={"backicon"}
          title="Verification Account"
          onLeftPress={() => setCurrentStep("main")}
        />

        <Screen
          style={[$root, { alignContent: "center" }]}
          preset="scroll"
          safeAreaEdges={["bottom"]}
          StatusBarProps={{
            backgroundColor: colors.palette.neutral900,
          }}
        >
          <UserAdvanceInfo
            onCompleted={() => {
              setCurrentStep("main")
            }}
            nav={navigation}
          />
        </Screen>
      </>
    )
  }

  if (currentStep === "documents") {
    return (
      <>
        <Header
          leftIcon={"backicon"}
          title="Verification Account"
          onLeftPress={() => setCurrentStep("main")}
        />

        <Screen
          style={[$root, { alignContent: "center", justifyContent: "center" }]}
          preset="fixed"
          safeAreaEdges={["bottom"]}
          StatusBarProps={{
            backgroundColor: colors.palette.neutral900,
          }}
        >
          <DocumentVerification
            onCompleted={() => {
              navigation.navigate("FedhaLoader" as never)
            }}
          />
        </Screen>
      </>
    )
  }

  // const openWhatsApp = () => {
  //   // Get current verification step message
  //   const stepMessage = (() => {
  //     if (!isBasicInfoComplete) return "Étape: Informations de base"
  //     if (!isAdvanceInfoComplete) return "Étape: Informations avancées"
  //     if (!isDocumentVerified) return "Étape: Vérification des documents"
  //     return "Étape: Vérification complète"
  //   })()

  //   // Format the message
  //   const message = `Bonjour, j'ai besoin d'aide avec la vérification de mon compte Fedha.\n${stepMessage}`

  //   // Replace with your actual WhatsApp number
  //   const phoneNumber = "+243836803506"

  //   // Create WhatsApp URL with encoded message
  //   const url = `https://wa.me/${phoneNumber.replace("+", "")}?text=${encodeURIComponent(message)}`

  //   Linking.openURL(url)
  //     .then((supported) => {
  //       if (supported) {
  //         return Linking.openURL(url)
  //       } else {
  //         alert("WhatsApp n'est pas installé sur votre appareil")
  //         return Promise.resolve() // Explicitly return a resolved promise
  //       }
  //     })
  //     .catch((err) => console.error("An error occurred", err))
  // }

  return (
    <Screen
      style={$root}
      preset="scroll"
      safeAreaEdges={["bottom", "top"]}
      StatusBarProps={{
        backgroundColor: colors.palette.neutral900,
      }}
    >
      <View
        style={{
          flexDirection: "row",
          justifyContent: "space-between",
          alignItems: "center",
          marginBottom: spacing.md,
        }}
      >
        <View style={{ flexDirection: "row", gap: spacing.sm }}>
          <Icon
            icon="helpcenter"
            size={39}
            // onPress={openWhatsApp}
            onPress={() => navigation.navigate("Helpcenter")}
            containerStyle={[$Gstyles.ActionIcon]}
          />
        </View>
        <Icon
          icon="logout"
          color={colors.palette.primary200}
          size={25}
          onPress={() => logout()}
          containerStyle={[$Gstyles.ActionIcon, { backgroundColor: colors.palette.primary600 }]}
        />
      </View>

      <View style={header}>
        <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
          <Text preset="heading" style={title}>
            Complétez{"\n"}votre profil
          </Text>
        </View>

        <View style={{ flexDirection: "row", justifyContent: "space-between", alignItems: "center" }}>
          <Text preset="subheading" style={subHeading}>
            {progress === 1.0 ? "Verification Complete" : "En Cours"}
          </Text>
        </View>
        <ProgressBar progress={progress} color={color} style={progressBar} />
      </View>

      <View style={$Gstyles.boxcomp}>
        <View>
          <Icon
            icon={"privacy"} // Adjust based on your icon names
            color={colors.palette.primary200}
            size={25}
            containerStyle={[$Gstyles.ActionIcon, { backgroundColor: colors.palette.primary600 }]}
          />
        </View>

        <View>
          <Text preset="subheading" style={serviceName}>
            Verification du compte
          </Text>
          <View>
            <Text preset="formHelper">
              {" "}
              {user?.verification_status === "Verified" ? "Verified ✅" : "En Progress ..."}
            </Text>
          </View>
        </View>
      </View>
      {isAllVerified &&
      <View style={$infoBox}>
        <Text preset="formHelper" style={$info}>
          Le processus de vérification peut prendre jusqu&lsquo;à 2-3 jours ouvrables.
          des documents supplémentaires peuvent être demandés par notre équipe de vérification.
        </Text>
      </View>
}
      <View>
        <Text preset="subheading" style={sectionHeader}>
          À FAIRE
        </Text>
        {!isAllVerified && (
          <View style={$Gstyles.boxcomp}>
            <View>
              <Icon
                icon={"accountV"}
                color={colors.palette.neutral900}
                size={25}
                containerStyle={$Gstyles.ActionIcon}
              />
            </View>

            <View>
              <Text preset="subheading" style={serviceName}>
                Vos Informations basic
              </Text>
              <View>
                {!isInfoVerified && (
                  <Button style={button} onPress={handleOpenVerification}>
                    {isBasicInfoComplete ? "Continuer..." : "Commencer"}
                  </Button>
                )}
              </View>
            </View>
          </View>
        )}
        {!user?.is_documents_verified && (
          <View style={$Gstyles.boxcomp}>
            <View>
              <Icon
                icon={"idcardVerif"}
                color={colors.palette.neutral900}
                size={25}
                containerStyle={$Gstyles.ActionIcon}
              />
            </View>

            <View>
              <Text preset="subheading" style={serviceName}>
                Vérifiez votre identité
              </Text>
              <View>
                {isAdvanceInfoComplete && isInfoVerified ? (
                  user?.IdentityDocument?.numero_de_piece ? (
                    <Button style={[button, { opacity: 0.5 }]} disabled={true}>
                      En attente de vérification...
                    </Button>
                  ) : (
                    <Button style={button} onPress={() => setCurrentStep("documents")}>
                      Commencer
                    </Button>
                  )
                ) : (
                  <Button style={[button, { opacity: 0.5 }]} disabled={true}>
                    {!isInfoVerified
                      ? "Complétez"
                      : "Complétez"
                    }
                  </Button>
                )}
              </View>
            </View>
          </View>
        )}
      </View>

      <View
        style={{
          paddingBottom: spacing.xxl,
        }}
      >
        <Text preset="subheading" style={sectionHeader}>
          COMPLÉTÉ
        </Text>
        {isAllVerified && (
          <View style={$Gstyles.boxcomp}>
            <View>
              <Icon
                icon={"check"}
                color={colors.palette.primary200}
                size={25}
                containerStyle={[
                  $Gstyles.ActionIcon,
                  { backgroundColor: colors.palette.primary600 },
                ]}
              />
            </View>

            <View>
              <Text preset="subheading" style={serviceName}>
                Informations Client
              </Text>
            </View>
          </View>
        )}
        {user?.is_phone_verified && (
          <View style={$Gstyles.boxcomp}>
            <View>
              <Icon
                icon={"check"}
                color={colors.palette.primary200}
                size={25}
                containerStyle={[
                  $Gstyles.ActionIcon,
                  { backgroundColor: colors.palette.primary600 },
                ]}
              />
            </View>

            <View>
              <Text preset="subheading" style={serviceName}>
                Numéro de téléphone Vérifier
              </Text>
            </View>
          </View>
        )}
        {user?.is_documents_verified && (
          <View style={$Gstyles.boxcomp}>
            <View>
              <Icon
                icon={"check"}
                color={colors.palette.primary200}
                size={25}
                containerStyle={[
                  $Gstyles.ActionIcon,
                  { backgroundColor: colors.palette.primary600 },
                ]}
              />
            </View>

            <View>
              <Text preset="subheading" style={serviceName}>
                Votre identité
              </Text>
            </View>
          </View>
        )}
      </View>

      {/* <Text text="userVerification" />
      <Button
        testID="next-screen-button"
        preset="reversed"
        text={"LOGOUT"}
        // // style={themed($buttonStyle2)}
        style={$Gstyles.buttonStyle} // Reduce opacity if disabled
        // disabled={isButtonDisabled}
        onPress={() => logout()}
      /> */}
    </Screen>
  )
}

const $root: ViewStyle = {
  flex: 1,
  paddingTop: spacing.xl,
  paddingBottom: spacing.xxl,
  paddingHorizontal: spacing.lg,
}

const $info: TextStyle = {
  color: "#555",
    fontSize: 14,
    marginBottom: 30,
}

const $infoBox: ViewStyle = {
  flexDirection: "row",
    alignContent: "center",
    backgroundColor: "#f2f2f2",
    borderRadius: 15,
    padding: 10,
}