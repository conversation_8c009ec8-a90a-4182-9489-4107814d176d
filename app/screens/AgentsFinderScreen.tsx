import { FC } from "react"
import { ViewStyle } from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Screen, Text } from "@/components"
// import { useNavigation } from "@react-navigation/native"

interface AgentsFinderScreenProps extends AppStackScreenProps<"AgentsFinder"> {}


export const AgentsFinderScreen: FC<AgentsFinderScreenProps> = () => {

  // Pull in navigation via hook
  // const navigation = useNavigation()
  return (
    <Screen style={$root} preset="scroll">
      <Text text="agentsFinder" />
    </Screen>
  )

}

const $root: ViewStyle = {
  flex: 1,
}
