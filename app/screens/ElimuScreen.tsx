/* eslint-disable react-native/no-unused-styles */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-restricted-imports */
/* eslint-disable react-native/sort-styles */
/* eslint-disable react-native/no-color-literals */
import { FC, useMemo, useState, useRef, useEffect } from "react"
import {
  View,
  ScrollView,
  Image,
  ViewStyle,
  TextStyle,
  ImageStyle,
  TouchableOpacity,
  Modal,
  Animated,
  Dimensions,
  StatusBar,
} from "react-native"

// WebView is required at runtime (YouTube). We will lazy-require it to avoid type issues if not installed.
import { AppStackScreenProps } from "@/navigators"
import { Screen, Header, Text, Button, Icon } from "@/components"
import React from "react"
import { colors } from "@/theme"

const { width: screenWidth, height: screenHeight } = Dimensions.get("window")

interface ElimuScreenProps extends AppStackScreenProps<"Elimu"> {}

export const ElimuScreen: FC<ElimuScreenProps> = ({ navigation }) => {
  const [viewerVisible, setViewerVisible] = useState(false)
  const [currentIndex, setCurrentIndex] = useState(0)
  const [detailsVisible, setDetailsVisible] = useState(false)

  const videos = useMemo(
    () =>
      [
        {
          id: "v1",
          title: "Comment configurer un compte d’épargne sur Fedha",
          image: require("../../assets/images/people/business.png"),
          duration: "4:30",
          available: true,
          watched: false,
          likes: 120,
          description:
            "Apprenez à créer et alimenter un compte d’épargne sur Fedha en quelques étapes.",
          guests: ["Coach Amina"] as string[],
          tags: ["épargne", "objectifs", "sécurité"] as string[],
          youtubeUrl:
            "https://www.youtube.com/watch?v=GUkRGwQ_Qa8&list=PL7e7g-193i7hAlnUYLdc_2DgngKarJOoF",
        },
        {
          id: "v2",
          title: "Naviguer dans l’application Fedha",
          image: require("../../assets/images/people/bsuiness.jpg"),
          duration: "6:10",
          available: false,
          watched: false,
          likes: 0,
          description:
            "Découvrez les fonctionnalités clés de Fedha et comment s’y retrouver facilement.",
          guests: ["Équipe Produit"] as string[],
          tags: ["onboarding", "navigation"] as string[],
          youtubeUrl:
            "https://www.youtube.com/watch?v=GUkRGwQ_Qa8&list=PL7e7g-193i7hAlnUYLdc_2DgngKarJOoF",
        },
        {
          id: "v3",
          title: "Épargner automatiquement chaque semaine",
          image: require("../../assets/images/people/familly.png"),
          duration: "3:05",
          available: true,
          watched: true,
          likes: 87,
          description: "Mettez en place des épargnes récurrentes et suivez votre progrès.",
          guests: [] as string[],
          tags: ["automatisation", "budget"] as string[],
          youtubeUrl:
            "https://www.youtube.com/watch?v=GUkRGwQ_Qa8&list=PL7e7g-193i7hAlnUYLdc_2DgngKarJOoF",
        },
      ] as const,
    [],
  )

  const [likesState, setLikesState] = useState<Record<string, { count: number; liked: boolean }>>(
    () => Object.fromEntries(videos.map((v) => [v.id, { count: v.likes, liked: false }])),
  )

  function openViewer(index: number) {
    setCurrentIndex(index)
    if (!videos[index].available) {
      setDetailsVisible(true)
      return
    }
    setViewerVisible(true)
  }
  return (
    <>
      <Header
        title="Elimu"
        leftIcon="backicon"
        onLeftPress={() => navigation.goBack()}
        rightIcon="info"
      />

      <Screen style={$root} preset="scroll" statusBarStyle="dark">
        {/* Académie - carte principale */}
        <View style={$section}>
          <Text preset="heading" style={$pageTitle}>
            Apprendre
          </Text>

          <View
            // source={require("../../assets/images/banners/DefaultBackground.png")}
            style={$hero}
            // imageStyle={$heroImage}
            // resizeMode="cover"
          ></View>
          <View>
            <View style={$badgeRow}>
              <Icon icon="elimu" size={16} color={colors.palette.primary600} />
              <Text style={$badgeText}>Elimu</Text>
            </View>

            <Text weight="bold" size="xl" style={$cardTitle}>
              Bases financières
            </Text>
            <Text style={$cardDescription}>
              C’est l’endroit idéal pour commencer pour toute personne nouvelle aux sujets d’argent.
              Nous couvrons les notions essentielles comme l’argent, la richesse, le crédit,
              l’inflation et plus encore.
            </Text>

            <Button
              text="Aller à notre académie"
              style={$cta}
              textStyle={{ color: colors.palette.neutral100 }}
              onPress={() => {}}
              preset="reversed"
            />
          </View>

          {/* Étiquettes */}
          {/* <View style={$chipsWrap}>
            {[
              "Investir",
              "Investir pour votre enfant",
              "Style de vie",
              "Conseils d’argent",
              "Étudiants",
            ].map((label) => (
              <View key={label} style={$chip}>
                <Text style={$chipText}>{label}</Text>
              </View>
            ))}
          </View> */}
        </View>

        {/* Regarder et apprendre */}
        <View style={$divider} />
        <View style={$section}>
          <SectionHeader
            icon="lightbulb"
            color={colors.palette.secondsMain}
            title="REGARDER ET APPRENDRE"
          />

          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={$hList}
          >
            {videos.map((v, index) => (
              <VideoCard
                key={v.id}
                title={v.title}
                image={v.image}
                duration={v.duration}
                available={v.available}
                watched={v.watched}
                onPress={() => openViewer(index)}
              />
            ))}
          </ScrollView>

          <Button
            text="Voir toutes les vidéos"
            style={[$seeAll, { backgroundColor: colors.palette.secondsMain }]}
            textStyle={{ color: colors.palette.neutral100 }}
            onPress={() => {}}
          />
        </View>

        {/* Écouter et apprendre */}
        <View style={$divider} />
        <View style={$section}>
          <SectionHeader icon="lightbulb" color="#C000FF" title="ÉCOUTER ET APPRENDRE" />

          {[1, 2].map((i) => (
            <View key={`ep-${i}`} style={$podcastItem}>
              <Text style={$episodeLabel}>ÉPISODE {i}</Text>
              <Text weight="bold" size="lg" style={$podcastTitle}>
                De la famille à l’indépendance financière: l’histoire d’investissement de Dudu
              </Text>
              <Text style={$podcastExcerpt}>
                Dans cet épisode, nous discutons avec une investisseuse de son parcours financier,
                des premières leçons apprises et de la gestion des revenus.
              </Text>
              <Text style={$podcastMeta}>37 min d’écoute</Text>
            </View>
          ))}
        </View>
        {/* Visionneuse & Quiz */}
        <VideoViewer
          visible={viewerVisible}
          onClose={() => setViewerVisible(false)}
          video={videos[currentIndex]}
          likes={likesState[videos[currentIndex].id]?.count ?? 0}
          liked={likesState[videos[currentIndex].id]?.liked ?? false}
          onToggleLike={() =>
            setLikesState((s) => {
              const id = videos[currentIndex].id
              const prev = s[id] || { count: 0, liked: false }
              const next = { count: prev.count + (prev.liked ? -1 : 1), liked: !prev.liked }
              return { ...s, [id]: next }
            })
          }
        />
        <VideoDetailsModal
          visible={detailsVisible}
          onClose={() => setDetailsVisible(false)}
          video={videos[currentIndex]}
        />
      </Screen>
    </>
  )
}

const $root: ViewStyle = {
  paddingHorizontal: 16,
  // backgroundColor: colors.palette.neutral100,
}

const $section: ViewStyle = {
  paddingTop: 8,
  paddingBottom: 16,
}

const $pageTitle: TextStyle = {
  marginTop: 8,
  marginBottom: 8,
}

const $hero: ViewStyle = {
  width: "100%",
  borderRadius: 12,
  padding: 16,
  backgroundColor: colors.palette.primary100,
}

const $badgeRow: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
}

const $badgeText: TextStyle = {
  color: colors.palette.primary600,
  marginLeft: 6,
  fontWeight: "600",
}

const $cardTitle: TextStyle = {
  marginTop: 8,
  marginBottom: 8,
}

const $cardDescription: TextStyle = {
  color: colors.text,
  opacity: 0.85,
}

const $cta: ViewStyle = {
  marginTop: 16,
  backgroundColor: colors.palette.primary500,
  borderRadius: 10,
}

const $divider: ViewStyle = {
  height: 1,
  backgroundColor: colors.palette.accent300,
  marginVertical: 8,
}

const $hList: ViewStyle = {
  paddingVertical: 8,
}

const $videoCard: ViewStyle = {
  width: 260,
  marginRight: 16,
}

const $videoImage: ImageStyle = {
  width: 260,
  height: 150,
  borderRadius: 12,
}

const $videoTitle: TextStyle = {
  marginTop: 8,
}

const $seeAll: ViewStyle = {
  marginTop: 8,
  borderRadius: 12,
}

const $podcastItem: ViewStyle = {
  marginTop: 12,
}

const $episodeLabel: TextStyle = {
  color: colors.palette.secondary500,
  marginBottom: 4,
}

const $podcastTitle: TextStyle = {}

const $podcastExcerpt: TextStyle = {
  marginTop: 6,
  opacity: 0.9,
}

const $podcastMeta: TextStyle = {
  marginTop: 6,
  color: colors.textDim,
}

interface SectionHeaderProps {
  icon: keyof typeof import("@/components/atoms/Icon").iconRegistry
  color: string
  title: string
}

function SectionHeader(props: SectionHeaderProps) {
  const { icon, color, title } = props
  return (
    <View style={$sectionHeader}>
      <Icon icon={icon as any} size={18} color={color} />
      <Text style={[$sectionHeaderText, { color }]} weight="medium">
        {title}
      </Text>
    </View>
  )
}

const $sectionHeader: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  marginBottom: 8,
}

const $sectionHeaderText: TextStyle = {
  marginLeft: 8,
}

// ===== Vidéo: Carte, Visionneuse & Quiz =====

interface VideoCardProps {
  title: string
  image: any
  duration: string
  available: boolean
  watched: boolean
  onPress: () => void
}

function VideoCard(props: VideoCardProps) {
  const { title, image, duration, available, watched, onPress } = props
  const scaleAnim = useRef(new Animated.Value(1)).current

  const handlePressIn = () => {
    if (available) {
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }).start()
    }
  }

  const handlePressOut = () => {
    if (available) {
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }).start()
    }
  }

  return (
    <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
      <TouchableOpacity
        activeOpacity={available ? 0.85 : 1}
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        style={$videoCard}
      >
        <Image source={image} style={$videoImage} />

      <View style={[$overlay, !available && $overlayDim]} pointerEvents="none">
        <View style={$durationBadge}>
          <Text weight="medium" style={$durationText}>
            {duration}
          </Text>
        </View>

        {available ? (
          <View style={$centerPlay}>
            <View style={$playCircle}>
              <Icon icon="play" size={24} color={colors.palette.neutral100} />
            </View>
          </View>
        ) : (
          <View style={$comingTag}>
            <Text weight="medium" style={$comingText}>
              Bientôt
            </Text>
          </View>
        )}

        {watched && (
          <View style={$watchedTick}>
            <View style={$tickCircle}>
              <Icon icon="check" size={16} color={colors.palette.neutral800} />
            </View>
          </View>
        )}
      </View>

      <Text weight="medium" style={$videoTitle}>
        {title}
      </Text>
    </TouchableOpacity>
    </Animated.View>
  )
}

interface VideoViewerProps {
  visible: boolean
  onClose: () => void
  video: {
    title: string
    image: any
    duration: string
    youtubeUrl?: string
  }
  likes: number
  liked: boolean
  onToggleLike: () => void
}

function VideoViewer(props: VideoViewerProps) {
  const { visible, onClose, video, likes, liked, onToggleLike } = props
  const [step, setStep] = useState<"watch" | "quiz" | "result">("watch")
  const [selected, setSelected] = useState<number | null>(null)
  const [qIndex, setQIndex] = useState(0)
  const [showControls, setShowControls] = useState(true)

  // Animation refs for TikTok-style transitions
  const slideAnim = useRef(new Animated.Value(screenHeight)).current
  const fadeAnim = useRef(new Animated.Value(0)).current
  const controlsOpacity = useRef(new Animated.Value(1)).current
  const likeScale = useRef(new Animated.Value(1)).current

  const questions: { q: string; options: string[]; answer: number }[] = [
    {
      q: "Quel est l’objectif principal de cette leçon?",
      options: ["Épargner", "Dépenser", "Ignorer les budgets"],
      answer: 0,
    },
    {
      q: "Combien de minutes dure la vidéo?",
      options: [video.duration, "2:00", "10:00"],
      answer: 0,
    },
  ] as const

  // Animation effects
  useEffect(() => {
    if (visible) {
      // Slide up animation
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start()
    } else {
      // Slide down animation
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: screenHeight,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 250,
          useNativeDriver: true,
        }),
      ]).start()
    }
  }, [visible, slideAnim, fadeAnim])

  // Auto-hide controls after 3 seconds
  useEffect(() => {
    if (step === "watch" && showControls) {
      const timer = setTimeout(() => {
        Animated.timing(controlsOpacity, {
          toValue: 0.3,
          duration: 300,
          useNativeDriver: true,
        }).start()
        setShowControls(false)
      }, 3000)
      return () => clearTimeout(timer)
    }
    return undefined
  }, [step, showControls, controlsOpacity])

  // Show controls on tap
  const handleScreenTap = () => {
    if (!showControls) {
      setShowControls(true)
      Animated.timing(controlsOpacity, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }).start()
    }
  }

  // Enhanced like animation
  const handleLikePress = () => {
    Animated.sequence([
      Animated.timing(likeScale, {
        toValue: 1.3,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(likeScale, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start()
    onToggleLike()
  }

  function submitOrNext() {
    if (step === "watch") {
      setStep("quiz")
      return
    }
    if (qIndex < questions.length - 1) {
      setQIndex(qIndex + 1)
      setSelected(null)
      return
    }
    setStep("result")
  }

  return (
    <Modal
      visible={visible}
      animationType="none"
      onRequestClose={onClose}
      presentationStyle="fullScreen"
      transparent={true}
    >
      <StatusBar hidden />
      <Animated.View
        style={[
          $tikTokViewerRoot,
          {
            transform: [{ translateY: slideAnim }],
            opacity: fadeAnim,
          },
        ]}
      >
        {step === "watch" && (
          <TouchableOpacity
            style={$tikTokVideoContainer}
            activeOpacity={1}
            onPress={handleScreenTap}
          >
            {/* Video Content */}
            <View style={$videoContentContainer}>
              {video.youtubeUrl ? (
                // Lazy load WebView to avoid static import type errors if the module isn't present in types
                // eslint-disable-next-line @typescript-eslint/no-var-requires
                (() => {
                  const { WebView } = require("react-native-webview")
                  return (
                    <WebView
                      style={$tikTokWebview}
                      source={{ uri: video.youtubeUrl.replace("watch?v=", "embed/") }}
                      allowsFullscreenVideo
                    />
                  )
                })()
              ) : (
                <Image source={video.image} style={$tikTokVideoImage} />
              )}
            </View>

            {/* TikTok-style Overlay Controls */}
            <Animated.View style={[$tikTokOverlay, { opacity: controlsOpacity }]}>
              {/* Close Button */}
              <TouchableOpacity style={$tikTokCloseButton} onPress={onClose}>
                <Icon icon="x" size={24} color={colors.palette.neutral100} />
              </TouchableOpacity>

              {/* Right Side Actions (TikTok-style) */}
              <View style={$tikTokRightActions}>
                {/* Like Button */}
                <Animated.View style={{ transform: [{ scale: likeScale }] }}>
                  <TouchableOpacity onPress={handleLikePress} style={$tikTokActionButton}>
                    <Icon
                      icon={liked ? "likefull" : "heart"}
                      size={28}
                      color={liked ? colors.palette.secondsMain : colors.palette.neutral100}
                    />
                  </TouchableOpacity>
                </Animated.View>
                <Text style={$tikTokActionText}>{likes}</Text>

                {/* Share Button */}
                <TouchableOpacity style={$tikTokActionButton}>
                  <Icon icon="share" size={28} color={colors.palette.neutral100} />
                </TouchableOpacity>
                <Text style={$tikTokActionText}>Partager</Text>

                {/* More Options */}
                <TouchableOpacity style={$tikTokActionButton}>
                  <Icon icon="more" size={28} color={colors.palette.neutral100} />
                </TouchableOpacity>
              </View>

              {/* Bottom Info */}
              <View style={$tikTokBottomInfo}>
                <Text weight="bold" size="lg" style={$tikTokVideoTitle}>
                  {video.title}
                </Text>

                {/* Horizontal Metrics Scroll */}
                <ScrollView
                  horizontal
                  showsHorizontalScrollIndicator={false}
                  style={$metricsScroll}
                  contentContainerStyle={$metricsContainer}
                >
                  <View style={$metricItem}>
                    <Icon icon="T0clock" size={16} color={colors.palette.neutral100} />
                    <Text style={$metricText}>{video.duration}</Text>
                  </View>
                  <View style={$metricItem}>
                    <Icon icon="heart" size={16} color={colors.palette.neutral100} />
                    <Text style={$metricText}>{likes} j&apos;aime</Text>
                  </View>
                  <View style={$metricItem}>
                    <Icon icon="view" size={16} color={colors.palette.neutral100} />
                    <Text style={$metricText}>1.2k vues</Text>
                  </View>
                </ScrollView>

                <Button
                  text="Terminer la vidéo"
                  style={$tikTokContinueButton}
                  textStyle={$tikTokContinueButtonText}
                  onPress={submitOrNext}
                />
              </View>
            </Animated.View>
          </TouchableOpacity>
        )}

        {step === "quiz" && (
          <View style={$tikTokQuizContainer}>
            <View style={$quizContent}>
              <Text weight="bold" size="lg" style={$quizTitle}>
                Quiz rapide
              </Text>
              <Text style={$tikTokQuizQuestion}>{questions[qIndex].q}</Text>
              <View style={$quizOptionsWrap}>
                {questions[qIndex].options.map((opt, i) => (
                  <Button
                    key={opt}
                    text={opt}
                    style={[$tikTokQuizOption, selected === i && $quizOptionSelected]}
                    textStyle={$tikTokQuizOptionText}
                    onPress={() => setSelected(i)}
                  />
                ))}
              </View>
              <Button
                text={qIndex < questions.length - 1 ? "Suivant" : "Voir le résultat"}
                onPress={submitOrNext}
                style={$tikTokQuizNavButton}
                textStyle={$tikTokQuizNavButtonText}
              />
            </View>
            <TouchableOpacity style={$tikTokQuizCloseButton} onPress={onClose}>
              <Icon icon="x" size={24} color={colors.palette.neutral100} />
            </TouchableOpacity>
          </View>
        )}

        {step === "result" && (
          <View style={$tikTokResultContainer}>
            <View style={$resultContent}>
              <Text weight="bold" size="lg" style={$resultTitle}>
                Résultat
              </Text>
              <Text style={$tikTokResultText}>Merci d’avoir suivi la leçon.</Text>
              <Button
                text="Fermer"
                style={$tikTokResultButton}
                textStyle={$tikTokResultButtonText}
                onPress={onClose}
              />
            </View>
          </View>
        )}
      </Animated.View>
    </Modal>
  )
}

interface VideoDetailsModalProps {
  visible: boolean
  onClose: () => void
  video: {
    title: string
    image: any
    duration: string
    description?: string
    guests?: string[]
    tags?: string[]
  }
}

function VideoDetailsModal(props: VideoDetailsModalProps) {
  const { visible, onClose, video } = props
  return (
    <Modal visible={visible} animationType="slide" onRequestClose={onClose}>
      <View style={$viewerBody}>
        <Image source={video.image} style={$viewerImage} />
        <Text weight="bold" size="lg" style={$viewerTitle}>
          {video.title}
        </Text>
        <Text style={$quizQuestion}>{video.description}</Text>
        {!!video.guests?.length && (
          <Text style={$quizQuestion}>Invités: {video.guests.join(", ")}</Text>
        )}
        {!!video.tags?.length && <Text style={$quizQuestion}>Sujets: {video.tags.join(", ")}</Text>}
        <Button text="Fermer" style={$viewerButton} onPress={onClose} />
      </View>
    </Modal>
  )
}

const $overlay: ViewStyle = {
  position: "absolute",
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  borderRadius: 12,
}

const $overlayDim: ViewStyle = {
  backgroundColor: "rgba(0,0,0,0.35)",
}

const $durationBadge: ViewStyle = {
  position: "absolute",
  left: 8,
  bottom: 8,
  backgroundColor: "rgba(0,0,0,0.55)",
  borderRadius: 6,
  paddingHorizontal: 8,
  paddingVertical: 4,
}

const $durationText: TextStyle = {
  color: colors.palette.neutral100,
}

const $centerPlay: ViewStyle = {
  flex: 1,
  alignItems: "center",
  justifyContent: "center",
}

const $playCircle: ViewStyle = {
  backgroundColor: "rgba(8,44,44,0.55)",
  width: 48,
  height: 48,
  borderRadius: 24,
  alignItems: "center",
  justifyContent: "center",
}

const $watchedTick: ViewStyle = {
  position: "absolute",
  right: 8,
  bottom: 8,
}

const $tickCircle: ViewStyle = {
  width: 28,
  height: 28,
  borderRadius: 14,
  backgroundColor: colors.palette.neutral100,
  alignItems: "center",
  justifyContent: "center",
}

const $comingTag: ViewStyle = {
  position: "absolute",
  right: 8,
  top: 8,
  backgroundColor: colors.palette.secondary500,
  borderRadius: 14,
  paddingHorizontal: 10,
  paddingVertical: 6,
}

const $comingText: TextStyle = {
  color: colors.palette.neutral100,
}

const $viewerBody: ViewStyle = {
  flex: 1,
  alignItems: "center",
  justifyContent: "center",
  padding: 16,
}

const $viewerImage: ImageStyle = {
  width: "100%",
  height: 300,
  borderRadius: 12,
}

const $viewerTitle: TextStyle = { marginTop: 12 }

const $viewerButton: ViewStyle = { marginTop: 16 }

const $quizQuestion: TextStyle = { marginTop: 8 }

const $quizOptionsWrap: ViewStyle = { marginTop: 12, width: "100%" }

const $quizOptionSelected: ViewStyle = { backgroundColor: colors.palette.primary200 }

// ===== TikTok-Style Video Player Styles =====

const $tikTokViewerRoot: ViewStyle = {
  flex: 1,
  backgroundColor: colors.palette.neutral800,
}

const $tikTokVideoContainer: ViewStyle = {
  flex: 1,
  position: "relative",
}

const $videoContentContainer: ViewStyle = {
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
}

const $tikTokWebview: ViewStyle = {
  width: screenWidth,
  height: screenHeight * 0.7,
}

const $tikTokVideoImage: ImageStyle = {
  width: screenWidth,
  height: screenHeight * 0.7,
  resizeMode: "cover",
}

const $tikTokOverlay: ViewStyle = {
  position: "absolute",
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  justifyContent: "space-between",
}

const $tikTokCloseButton: ViewStyle = {
  position: "absolute",
  top: 50,
  left: 20,
  width: 40,
  height: 40,
  borderRadius: 20,
  backgroundColor: "rgba(0, 0, 0, 0.5)",
  alignItems: "center",
  justifyContent: "center",
}

const $tikTokRightActions: ViewStyle = {
  position: "absolute",
  right: 20,
  bottom: 120,
  alignItems: "center",
}

const $tikTokActionButton: ViewStyle = {
  width: 50,
  height: 50,
  borderRadius: 25,
  backgroundColor: "rgba(0, 0, 0, 0.6)",
  alignItems: "center",
  justifyContent: "center",
  marginBottom: 8,
}

const $tikTokActionText: TextStyle = {
  color: colors.palette.neutral100,
  fontSize: 12,
  textAlign: "center",
  marginBottom: 16,
}

const $tikTokBottomInfo: ViewStyle = {
  position: "absolute",
  bottom: 40,
  left: 20,
  right: 80,
}

const $tikTokVideoTitle: TextStyle = {
  color: colors.palette.neutral100,
  marginBottom: 12,
  textShadowColor: "rgba(0, 0, 0, 0.8)",
  textShadowOffset: { width: 1, height: 1 },
  textShadowRadius: 3,
}

const $metricsScroll: ViewStyle = {
  marginBottom: 16,
}

const $metricsContainer: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
}

const $metricItem: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: "rgba(0, 0, 0, 0.6)",
  paddingHorizontal: 12,
  paddingVertical: 6,
  borderRadius: 20,
  marginRight: 12,
}

const $metricText: TextStyle = {
  color: colors.palette.neutral100,
  fontSize: 12,
  marginLeft: 4,
}

const $tikTokContinueButton: ViewStyle = {
  backgroundColor: colors.palette.primary500,
  borderRadius: 25,
  paddingVertical: 12,
}

const $tikTokContinueButtonText: TextStyle = {
  color: colors.palette.neutral100,
  fontWeight: "600",
}

// ===== TikTok-Style Quiz Styles =====

const $tikTokQuizContainer: ViewStyle = {
  flex: 1,
  backgroundColor: colors.palette.neutral800,
  justifyContent: "center",
  alignItems: "center",
  padding: 20,
}

const $quizContent: ViewStyle = {
  width: "100%",
  maxWidth: 400,
  alignItems: "center",
}

const $quizTitle: TextStyle = {
  color: colors.palette.neutral100,
  marginBottom: 20,
  textAlign: "center",
}

const $tikTokQuizQuestion: TextStyle = {
  color: colors.palette.neutral100,
  fontSize: 18,
  textAlign: "center",
  marginBottom: 24,
  lineHeight: 24,
}

const $tikTokQuizOption: ViewStyle = {
  backgroundColor: "rgba(255, 255, 255, 0.1)",
  borderRadius: 12,
  marginBottom: 12,
  borderWidth: 1,
  borderColor: "rgba(255, 255, 255, 0.2)",
}

const $tikTokQuizOptionText: TextStyle = {
  color: colors.palette.neutral100,
}

const $tikTokQuizNavButton: ViewStyle = {
  backgroundColor: colors.palette.primary500,
  borderRadius: 25,
  marginTop: 20,
  paddingVertical: 14,
}

const $tikTokQuizNavButtonText: TextStyle = {
  color: colors.palette.neutral100,
  fontWeight: "600",
}

const $tikTokQuizCloseButton: ViewStyle = {
  position: "absolute",
  top: 50,
  right: 20,
  width: 40,
  height: 40,
  borderRadius: 20,
  backgroundColor: "rgba(0, 0, 0, 0.5)",
  alignItems: "center",
  justifyContent: "center",
}

// ===== TikTok-Style Result Styles =====

const $tikTokResultContainer: ViewStyle = {
  flex: 1,
  backgroundColor: colors.palette.neutral800,
  justifyContent: "center",
  alignItems: "center",
  padding: 20,
}

const $resultContent: ViewStyle = {
  width: "100%",
  maxWidth: 400,
  alignItems: "center",
}

const $resultTitle: TextStyle = {
  color: colors.palette.neutral100,
  marginBottom: 20,
  textAlign: "center",
}

const $tikTokResultText: TextStyle = {
  color: colors.palette.neutral100,
  fontSize: 16,
  textAlign: "center",
  marginBottom: 30,
  lineHeight: 22,
}

const $tikTokResultButton: ViewStyle = {
  backgroundColor: colors.palette.primary500,
  borderRadius: 25,
  paddingVertical: 14,
  paddingHorizontal: 40,
}

const $tikTokResultButtonText: TextStyle = {
  color: colors.palette.neutral100,
  fontWeight: "600",
}
