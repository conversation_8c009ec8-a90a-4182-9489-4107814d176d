/* eslint-disable react-native/no-unused-styles */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-restricted-imports */
/* eslint-disable react-native/sort-styles */
/* eslint-disable react-native/no-color-literals */
import { FC, useMemo, useState } from "react"
import {
  View,
  ScrollView,
  Image,
  ImageBackground,
  ViewStyle,
  TextStyle,
  ImageStyle,
  TouchableOpacity,
  Modal,
} from "react-native"
import { AppStackScreenProps } from "@/navigators"
import { Screen, Header, Text, Button, Icon } from "@/components"
import React from "react"
import { colors } from "@/theme"

interface ElimuScreenProps extends AppStackScreenProps<"Elimu"> {}

export const ElimuScreen: FC<ElimuScreenProps> = ({ navigation }) => {
  const [viewerVisible, setViewerVisible] = useState(false)
  const [currentIndex, setCurrentIndex] = useState(0)
  const [detailsVisible, setDetailsVisible] = useState(false)

  const videos = useMemo(
    () =>
      [
        {
          id: "v1",
          title: "Comment configurer un compte d’épargne sur Fedha",
          image: require("../../assets/images/people/business.png"),
          duration: "4:30",
          available: true,
          watched: false,
        },
        {
          id: "v2",
          title: "Naviguer dans l’application Fedha",
          image: require("../../assets/images/people/bsuiness.jpg"),
          duration: "6:10",
          available: false,
          watched: false,
        },
        {
          id: "v3",
          title: "Épargner automatiquement chaque semaine",
          image: require("../../assets/images/people/familly.png"),
          duration: "3:05",
          available: true,
          watched: true,
        },
      ] as const,
    [],
  )

  function openViewer(index: number) {
    setCurrentIndex(index)
    if (!videos[index].available) {
      setDetailsVisible(true)
      return
    }
    setViewerVisible(true)
  }
  return (
    <>
      <Header
        title="Elimu"
        leftIcon="backicon"
        onLeftPress={() => navigation.goBack()}
        rightIcon="info"
      />

      <Screen style={$root} preset="scroll" statusBarStyle="dark">
        {/* Académie - carte principale */}
        <View style={$section}>
          <Text preset="heading" style={$pageTitle}>
            Apprendre
          </Text>

          <View
            // source={require("../../assets/images/banners/DefaultBackground.png")}
            style={$hero}
            // imageStyle={$heroImage}
            // resizeMode="cover"
          ></View>
          <View>
            <View style={$badgeRow}>
              <Icon icon="elimu" size={16} color={colors.palette.primary600} />
              <Text style={$badgeText}>Elimu</Text>
            </View>

            <Text weight="bold" size="xl" style={$cardTitle}>
              Bases financières
            </Text>
            <Text style={$cardDescription}>
              C’est l’endroit idéal pour commencer pour toute personne nouvelle aux sujets d’argent.
              Nous couvrons les notions essentielles comme l’argent, la richesse, le crédit,
              l’inflation et plus encore.
            </Text>

            <Button
              text="Aller à notre académie"
              style={$cta}
              textStyle={{ color: colors.palette.neutral100 }}
              onPress={() => {}}
              preset="reversed"
            />
          </View>

          {/* Étiquettes */}
          {/* <View style={$chipsWrap}>
            {[
              "Investir",
              "Investir pour votre enfant",
              "Style de vie",
              "Conseils d’argent",
              "Étudiants",
            ].map((label) => (
              <View key={label} style={$chip}>
                <Text style={$chipText}>{label}</Text>
              </View>
            ))}
          </View> */}
        </View>

        {/* Regarder et apprendre */}
        <View style={$divider} />
        <View style={$section}>
          <SectionHeader
            icon="lightbulb"
            color={colors.palette.secondsMain}
            title="REGARDER ET APPRENDRE"
          />

          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={$hList}
          >
            {videos.map((v, index) => (
              <VideoCard
                key={v.id}
                title={v.title}
                image={v.image}
                duration={v.duration}
                available={v.available}
                watched={v.watched}
                onPress={() => openViewer(index)}
              />
            ))}
          </ScrollView>

          <Button
            text="Voir toutes les vidéos"
            style={[$seeAll, { backgroundColor: colors.palette.secondsMain }]}
            textStyle={{ color: colors.palette.neutral100 }}
            onPress={() => {}}
          />
        </View>

        {/* Écouter et apprendre */}
        <View style={$divider} />
        <View style={$section}>
          <SectionHeader icon="lightbulb" color="#C000FF" title="ÉCOUTER ET APPRENDRE" />

          {[1, 2].map((i) => (
            <View key={`ep-${i}`} style={$podcastItem}>
              <Text style={$episodeLabel}>ÉPISODE {i}</Text>
              <Text weight="bold" size="lg" style={$podcastTitle}>
                De la famille à l’indépendance financière: l’histoire d’investissement de Dudu
              </Text>
              <Text style={$podcastExcerpt}>
                Dans cet épisode, nous discutons avec une investisseuse de son parcours financier,
                des premières leçons apprises et de la gestion des revenus.
              </Text>
              <Text style={$podcastMeta}>37 min d’écoute</Text>
            </View>
          ))}
        </View>
        {/* Visionneuse & Quiz */}
        <VideoViewer
          visible={viewerVisible}
          onClose={() => setViewerVisible(false)}
          video={videos[currentIndex]}
        />
        <VideoDetailsModal
          visible={detailsVisible}
          onClose={() => setDetailsVisible(false)}
          video={videos[currentIndex]}
        />
      </Screen>
    </>
  )
}

const $root: ViewStyle = {
  paddingHorizontal: 16,
  // backgroundColor: colors.palette.neutral100,
}

const $section: ViewStyle = {
  paddingTop: 8,
  paddingBottom: 16,
}

const $pageTitle: TextStyle = {
  marginTop: 8,
  marginBottom: 8,
}

const $hero: ViewStyle = {
  width: "100%",
  borderRadius: 12,
  padding: 16,
  backgroundColor: colors.palette.primary100,
}

const $heroImage: ImageStyle = {
  borderRadius: 12,
}

const $badgeRow: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
}

const $badgeText: TextStyle = {
  color: colors.palette.primary600,
  marginLeft: 6,
  fontWeight: "600",
}

const $cardTitle: TextStyle = {
  marginTop: 8,
  marginBottom: 8,
}

const $cardDescription: TextStyle = {
  color: colors.text,
  opacity: 0.85,
}

const $cta: ViewStyle = {
  marginTop: 16,
  backgroundColor: colors.palette.primary500,
  borderRadius: 10,
}

const $chipsWrap: ViewStyle = {
  flexDirection: "row",
  flexWrap: "wrap",
  gap: 12,
  marginTop: 16,
}

const $chip: ViewStyle = {
  backgroundColor: colors.palette.primary100,
  paddingVertical: 8,
  paddingHorizontal: 12,
  borderRadius: 20,
}

const $chipText: TextStyle = {
  color: colors.palette.primary600,
}

const $divider: ViewStyle = {
  height: 1,
  backgroundColor: colors.palette.accent300,
  marginVertical: 8,
}

const $hList: ViewStyle = {
  paddingVertical: 8,
}

const $videoCard: ViewStyle = {
  width: 260,
  marginRight: 16,
}

const $videoImage: ImageStyle = {
  width: 260,
  height: 150,
  borderRadius: 12,
}

const $videoTitle: TextStyle = {
  marginTop: 8,
}

const $seeAll: ViewStyle = {
  marginTop: 8,
  borderRadius: 12,
}

const $podcastItem: ViewStyle = {
  marginTop: 12,
}

const $episodeLabel: TextStyle = {
  color: colors.palette.secondary500,
  marginBottom: 4,
}

const $podcastTitle: TextStyle = {}

const $podcastExcerpt: TextStyle = {
  marginTop: 6,
  opacity: 0.9,
}

const $podcastMeta: TextStyle = {
  marginTop: 6,
  color: colors.textDim,
}

interface SectionHeaderProps {
  icon: keyof typeof import("@/components/atoms/Icon").iconRegistry
  color: string
  title: string
}

function SectionHeader(props: SectionHeaderProps) {
  const { icon, color, title } = props
  return (
    <View style={$sectionHeader}>
      <Icon icon={icon as any} size={18} color={color} />
      <Text style={[$sectionHeaderText, { color }]} weight="medium">
        {title}
      </Text>
    </View>
  )
}

const $sectionHeader: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  marginBottom: 8,
}

const $sectionHeaderText: TextStyle = {
  marginLeft: 8,
}

// ===== Vidéo: Carte, Visionneuse & Quiz =====

interface VideoCardProps {
  title: string
  image: any
  duration: string
  available: boolean
  watched: boolean
  onPress: () => void
}

function VideoCard(props: VideoCardProps) {
  const { title, image, duration, available, watched, onPress } = props
  return (
    <TouchableOpacity activeOpacity={available ? 0.85 : 1} onPress={onPress} style={$videoCard}>
      <Image source={image} style={$videoImage} />

      <View style={[$overlay, !available && $overlayDim]} pointerEvents="none">
        <View style={$durationBadge}>
          <Text weight="medium" style={$durationText}>
            {duration}
          </Text>
        </View>

        {available ? (
          <View style={$centerPlay}>
            <View style={$playCircle}>
              <Icon icon="play" size={24} color={colors.palette.neutral100} />
            </View>
          </View>
        ) : (
          <View style={$comingTag}>
            <Text weight="medium" style={$comingText}>
              Bientôt
            </Text>
          </View>
        )}

        {watched && (
          <View style={$watchedTick}>
            <View style={$tickCircle}>
              <Icon icon="check" size={16} color={colors.palette.neutral800} />
            </View>
          </View>
        )}
      </View>

      <Text weight="medium" style={$videoTitle}>
        {title}
      </Text>
    </TouchableOpacity>
  )
}

interface VideoViewerProps {
  visible: boolean
  onClose: () => void
  video: {
    title: string
    image: any
    duration: string
  }
}

function VideoViewer(props: VideoViewerProps) {
  const { visible, onClose, video } = props
  const [step, setStep] = useState<"watch" | "quiz" | "result">("watch")
  const [selected, setSelected] = useState<number | null>(null)
  const [qIndex, setQIndex] = useState(0)

  const questions = [
    {
      q: "Quel est l’objectif principal de cette leçon?",
      options: ["Épargner", "Dépenser", "Ignorer les budgets"],
      answer: 0,
    },
    {
      q: "Combien de minutes dure la vidéo?",
      options: [video.duration, "2:00", "10:00"],
      answer: 0,
    },
  ] as const

  function submitOrNext() {
    if (step === "watch") {
      setStep("quiz")
      return
    }
    if (qIndex < questions.length - 1) {
      setQIndex(qIndex + 1)
      setSelected(null)
      return
    }
    setStep("result")
  }

  return (
    <Modal
      visible={visible}
      animationType="slide"
      onRequestClose={onClose}
      presentationStyle="fullScreen"
    >
      <View style={$viewerRoot}>
        {step === "watch" && (
          <View style={$viewerBody}>
            <Image source={video.image} style={$viewerImage} />
            <Text weight="bold" size="lg" style={$viewerTitle}>
              {video.title}
            </Text>
            <Button text="Terminer la vidéo" style={$viewerButton} onPress={submitOrNext} />
          </View>
        )}

        {step === "quiz" && (
          <View style={$viewerBody}>
            <Text weight="bold" size="lg">
              Quiz rapide
            </Text>
            <Text style={$quizQuestion}>{questions[qIndex].q}</Text>
            <View style={$quizOptionsWrap}>
              {questions[qIndex].options.map((opt, i) => (
                <Button
                  key={opt}
                  text={opt}
                  style={[$quizOption, selected === i && $quizOptionSelected]}
                  onPress={() => setSelected(i)}
                />
              ))}
            </View>
            <Button
              text={qIndex < questions.length - 1 ? "Suivant" : "Voir le résultat"}
              onPress={submitOrNext}
              style={$quizNavButton}
            />
          </View>
        )}

        {step === "result" && (
          <View style={$viewerBody}>
            <Text weight="bold" size="lg">
              Résultat
            </Text>
            <Text style={$quizQuestion}>Merci d’avoir suivi la leçon.</Text>
            <Button text="Fermer" style={$viewerButton} onPress={onClose} />
          </View>
        )}

        <Icon icon="x" size={22} onPress={onClose} containerStyle={$viewerClose} />
      </View>
    </Modal>
  )
}

interface VideoDetailsModalProps {
  visible: boolean
  onClose: () => void
  video: {
    title: string
    image: any
    duration: string
    description?: string
    guests?: string[]
    tags?: string[]
  }
}

function VideoDetailsModal(props: VideoDetailsModalProps) {
  const { visible, onClose, video } = props
  return (
    <Modal visible={visible} animationType="slide" onRequestClose={onClose}>
      <View style={$viewerBody}>
        <Image source={video.image} style={$viewerImage} />
        <Text weight="bold" size="lg" style={$viewerTitle}>
          {video.title}
        </Text>
        <Text style={$quizQuestion}>{video.description}</Text>
        {!!video.guests?.length && (
          <Text style={$quizQuestion}>Invités: {video.guests.join(", ")}</Text>
        )}
        {!!video.tags?.length && <Text style={$quizQuestion}>Sujets: {video.tags.join(", ")}</Text>}
        <Button text="Fermer" style={$viewerButton} onPress={onClose} />
      </View>
    </Modal>
  )
}

const $overlay: ViewStyle = {
  position: "absolute",
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  borderRadius: 12,
}

const $overlayDim: ViewStyle = {
  backgroundColor: "rgba(0,0,0,0.35)",
}

const $durationBadge: ViewStyle = {
  position: "absolute",
  left: 8,
  bottom: 8,
  backgroundColor: "rgba(0,0,0,0.55)",
  borderRadius: 6,
  paddingHorizontal: 8,
  paddingVertical: 4,
}

const $durationText: TextStyle = {
  color: colors.palette.neutral100,
}

const $centerPlay: ViewStyle = {
  flex: 1,
  alignItems: "center",
  justifyContent: "center",
}

const $playCircle: ViewStyle = {
  backgroundColor: "rgba(8,44,44,0.55)",
  width: 48,
  height: 48,
  borderRadius: 24,
  alignItems: "center",
  justifyContent: "center",
}

const $watchedTick: ViewStyle = {
  position: "absolute",
  right: 8,
  bottom: 8,
}

const $tickCircle: ViewStyle = {
  width: 28,
  height: 28,
  borderRadius: 14,
  backgroundColor: colors.palette.neutral100,
  alignItems: "center",
  justifyContent: "center",
}

const $comingTag: ViewStyle = {
  position: "absolute",
  right: 8,
  top: 8,
  backgroundColor: colors.palette.secondary500,
  borderRadius: 14,
  paddingHorizontal: 10,
  paddingVertical: 6,
}

const $comingText: TextStyle = {
  color: colors.palette.neutral100,
}

const $viewerRoot: ViewStyle = {
  flex: 1,
  backgroundColor: colors.palette.neutral100,
}

const $viewerBody: ViewStyle = {
  flex: 1,
  alignItems: "center",
  justifyContent: "center",
  padding: 16,
}

const $viewerImage: ImageStyle = {
  width: "100%",
  height: 300,
  borderRadius: 12,
}

const $viewerTitle: TextStyle = { marginTop: 12 }

const $viewerButton: ViewStyle = { marginTop: 16 }

const $viewerClose: ViewStyle = {
  position: "absolute",
  top: 16,
  right: 16,
}

const $quizQuestion: TextStyle = { marginTop: 8 }

const $quizOptionsWrap: ViewStyle = { marginTop: 12, width: "100%" }

const $quizOption: ViewStyle = { marginTop: 8 }

const $quizOptionSelected: ViewStyle = { backgroundColor: colors.palette.primary200 }

const $quizNavButton: ViewStyle = { marginTop: 12 }
