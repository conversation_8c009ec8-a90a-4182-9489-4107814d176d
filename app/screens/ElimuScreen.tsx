/* eslint-disable react-native/no-unused-styles */
/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable no-restricted-imports */
/* eslint-disable react-native/sort-styles */
/* eslint-disable react-native/no-color-literals */
import { FC, useMemo, useState, useRef, useEffect } from "react"
import {
  View,
  ScrollView,
  Image,
  ViewStyle,
  TextStyle,
  ImageStyle,
  TouchableOpacity,
  Modal,
  Animated,
  Dimensions,
  StatusBar,
} from "react-native"
// Import Video component - will be lazy loaded to avoid type issues
// const Video = require('react-native-video').default;
import { AppStackScreenProps } from "@/navigators"
import { Screen, Header, Text, Button, Icon } from "@/components"
import React from "react"
import { colors } from "@/theme"

const { width: screenWidth, height: screenHeight } = Dimensions.get("window")

interface ElimuScreenProps extends AppStackScreenProps<"Elimu"> {}

export const ElimuScreen: FC<ElimuScreenProps> = ({ navigation }) => {
  const [viewerVisible, setViewerVisible] = useState(false)
  const [currentIndex, setCurrentIndex] = useState(0)
  const [detailsVisible, setDetailsVisible] = useState(false)

  const videos = useMemo(
    () =>
      [
        {
          id: "v1",
          title: "Comment configurer un compte d'épargne sur Fedha",
          image: require("../../assets/images/people/business.png"),
          duration: "4:30",
          available: true,
          watched: false,
          likes: 120,
          description:
            "Apprenez à créer et alimenter un compte d'épargne sur Fedha en quelques étapes.",
          guests: ["Coach Amina"] as string[],
          tags: ["épargne", "objectifs", "sécurité"] as string[],
          videoFile: require("../../assets/videos/elimu/sampleVi.mp4"),
        },
        {
          id: "v2",
          title: "Naviguer dans l'application Fedha",
          image: require("../../assets/images/people/bsuiness.jpg"),
          duration: "6:10",
          available: false,
          watched: false,
          likes: 0,
          description:
            "Découvrez les fonctionnalités clés de Fedha et comment s'y retrouver facilement.",
          guests: ["Équipe Produit"] as string[],
          tags: ["onboarding", "navigation"] as string[],
          videoFile: require("../../assets/videos/elimu/sampleVi.mp4"),
        },
        {
          id: "v3",
          title: "Épargner automatiquement chaque semaine",
          image: require("../../assets/images/people/familly.png"),
          duration: "3:05",
          available: true,
          watched: true,
          likes: 87,
          description: "Mettez en place des épargnes récurrentes et suivez votre progrès.",
          guests: [] as string[],
          tags: ["automatisation", "budget"] as string[],
          videoFile: require("../../assets/videos/elimu/sampleVi.mp4"),
        },
      ] as const,
    [],
  )

  const [likesState, setLikesState] = useState<Record<string, { count: number; liked: boolean }>>(
    () => Object.fromEntries(videos.map((v) => [v.id, { count: v.likes, liked: false }])),
  )

  const handleVideoWatched = (videoId: string) => {
    // Track video watch for analytics if needed
    console.log(`Video ${videoId} watched`)
  }

  const goToNextVideo = () => {
    const nextIndex = currentIndex + 1
    if (nextIndex < videos.length && videos[nextIndex].available) {
      setCurrentIndex(nextIndex)
    } else {
      setViewerVisible(false)
    }
  }

  function openViewer(index: number) {
    setCurrentIndex(index)
    if (!videos[index].available) {
      setDetailsVisible(true)
      return
    }
    setViewerVisible(true)
  }

  const handleShare = () => {
    console.log("Share video:", videos[currentIndex].title)
  }

  return (
    <Screen preset="scroll" contentContainerStyle={$root}>
      <Header title="Elimu" />

      <View style={$section}>
        <SectionHeader icon="video" color={colors.palette.primary500} title="Vidéos éducatives" />
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={$hList}>
          {videos.map((video, index) => (
            <VideoCard
              key={video.id}
              title={video.title}
              image={video.image}
              duration={video.duration}
              available={video.available}
              watched={video.watched}
              onPress={() => openViewer(index)}
            />
          ))}
        </ScrollView>
      </View>

      {/* Video Viewer */}
      <VideoViewer
        visible={viewerVisible}
        onClose={() => setViewerVisible(false)}
        video={videos[currentIndex]}
        likes={likesState[videos[currentIndex].id]?.count ?? 0}
        liked={likesState[videos[currentIndex].id]?.liked ?? false}
        onToggleLike={() =>
          setLikesState((s) => {
            const id = videos[currentIndex].id
            const prev = s[id] || { count: 0, liked: false }
            const next = { count: prev.count + (prev.liked ? -1 : 1), liked: !prev.liked }
            return { ...s, [id]: next }
          })
        }
        currentIndex={currentIndex}
        totalVideos={videos.length}
        onNextVideo={goToNextVideo}
        onVideoWatched={handleVideoWatched}
        onShare={handleShare}
      />
    </Screen>
  )
}

interface SectionHeaderProps {
  icon:
    | "video"
    | "heart"
    | "play"
    | "share"
    | "x"
    | "check"
    | "T0clock"
    | "view"
    | "more"
    | "likefull"
  color: string
  title: string
}

function SectionHeader(props: SectionHeaderProps) {
  const { icon, color, title } = props
  return (
    <View style={$sectionHeader}>
      <Icon icon={icon} size={20} color={color} />
      <Text weight="medium" style={$sectionHeaderText}>
        {title}
      </Text>
    </View>
  )
}

interface VideoCardProps {
  title: string
  image: any
  duration: string
  available: boolean
  watched: boolean
  onPress: () => void
}

function VideoCard(props: VideoCardProps) {
  const { title, image, duration, available, watched, onPress } = props
  const scaleAnim = useRef(new Animated.Value(1)).current

  const handlePressIn = () => {
    if (available) {
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 100,
        useNativeDriver: true,
      }).start()
    }
  }

  const handlePressOut = () => {
    if (available) {
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }).start()
    }
  }

  return (
    <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
      <TouchableOpacity
        activeOpacity={available ? 0.85 : 1}
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        style={$videoCard}
      >
        <Image source={image} style={$videoImage} />

        <View style={[$overlay, !available && $overlayDim]} pointerEvents="none">
          <View style={$durationBadge}>
            <Text weight="medium" style={$durationText}>
              {duration}
            </Text>
          </View>

          {available ? (
            <View style={$centerPlay}>
              <View style={$playCircle}>
                <Icon icon="play" size={24} color={colors.palette.neutral100} />
              </View>
            </View>
          ) : (
            <View style={$comingTag}>
              <Text weight="medium" style={$comingText}>
                Bientôt
              </Text>
            </View>
          )}

          {watched && (
            <View style={$watchedTick}>
              <View style={$tickCircle}>
                <Icon icon="check" size={16} color={colors.palette.neutral800} />
              </View>
            </View>
          )}
        </View>

        <Text weight="medium" style={$videoTitle}>
          {title}
        </Text>
      </TouchableOpacity>
    </Animated.View>
  )
}

interface VideoViewerProps {
  visible: boolean
  onClose: () => void
  video: {
    id: string
    title: string
    image: any
    duration: string
    videoFile: any
    description?: string
  }
  likes: number
  liked: boolean
  onToggleLike: () => void
  currentIndex: number
  totalVideos: number
  onNextVideo: () => void
  onVideoWatched: (videoId: string) => void
  onShare: () => void
}

function VideoViewer(props: VideoViewerProps) {
  const {
    visible,
    onClose,
    video,
    likes,
    liked,
    onToggleLike,
    currentIndex,
    totalVideos,
    onNextVideo,
    onVideoWatched,
    onShare,
  } = props

  const [showControls, setShowControls] = useState(true)
  const [videoEnded, setVideoEnded] = useState(false)
  const [videoDuration, setVideoDuration] = useState(0)
  const [currentTime, setCurrentTime] = useState(0)

  // Animation refs for TikTok-style transitions
  const slideAnim = useRef(new Animated.Value(screenHeight)).current
  const fadeAnim = useRef(new Animated.Value(0)).current
  const controlsOpacity = useRef(new Animated.Value(1)).current
  const likeScale = useRef(new Animated.Value(1)).current

  // Video player functions
  const handleVideoEnd = () => {
    setVideoEnded(true)
    onVideoWatched(video.id)
  }

  const handleRewatch = () => {
    setVideoEnded(false)
    setCurrentTime(0)
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, "0")}`
  }

  // Animation effects
  useEffect(() => {
    if (visible) {
      // Reset video state when opening
      setVideoEnded(false)
      setCurrentTime(0)
      setShowControls(true)

      // Slide up animation
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start()
    } else {
      // Slide down animation
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: screenHeight,
          duration: 250,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 250,
          useNativeDriver: true,
        }),
      ]).start()
    }
  }, [visible, slideAnim, fadeAnim])

  // Auto-hide controls after 3 seconds
  useEffect(() => {
    if (showControls && !videoEnded) {
      const timer = setTimeout(() => {
        Animated.timing(controlsOpacity, {
          toValue: 0.3,
          duration: 300,
          useNativeDriver: true,
        }).start()
        setShowControls(false)
      }, 3000)
      return () => clearTimeout(timer)
    }
    return undefined
  }, [showControls, videoEnded, controlsOpacity])

  // Show controls on tap
  const handleScreenTap = () => {
    if (!showControls) {
      setShowControls(true)
      Animated.timing(controlsOpacity, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }).start()
    }
  }

  // Enhanced like animation
  const handleLikePress = () => {
    Animated.sequence([
      Animated.timing(likeScale, {
        toValue: 1.3,
        duration: 100,
        useNativeDriver: true,
      }),
      Animated.timing(likeScale, {
        toValue: 1,
        duration: 100,
        useNativeDriver: true,
      }),
    ]).start()
    onToggleLike()
  }

  return (
    <Modal
      visible={visible}
      animationType="none"
      onRequestClose={onClose}
      presentationStyle="fullScreen"
      transparent={true}
    >
      <StatusBar hidden />
      <Animated.View
        style={[
          $tikTokViewerRoot,
          {
            transform: [{ translateY: slideAnim }],
            opacity: fadeAnim,
          },
        ]}
      >
        <TouchableOpacity style={$tikTokVideoContainer} activeOpacity={1} onPress={handleScreenTap}>
          {/* Video Content */}
          <View style={$videoContentContainer}>
            {video.videoFile ? (
              // Use a simple Image for now - will be replaced with actual video player
              <Image source={video.videoFile} style={$tikTokVideoImage} />
            ) : (
              <Image source={video.image} style={$tikTokVideoImage} />
            )}
          </View>

          {/* TikTok-style Overlay Controls */}
          <Animated.View style={[$tikTokOverlay, { opacity: controlsOpacity }]}>
            {/* Close Button */}
            <TouchableOpacity style={$tikTokCloseButton} onPress={onClose}>
              <Icon icon="x" size={24} color={colors.palette.neutral100} />
            </TouchableOpacity>

            {/* Right Side Actions (TikTok-style) */}
            <View style={$tikTokRightActions}>
              {/* Like Button */}
              <Animated.View style={{ transform: [{ scale: likeScale }] }}>
                <TouchableOpacity onPress={handleLikePress} style={$tikTokActionButton}>
                  <Icon
                    icon={liked ? "likefull" : "heart"}
                    size={28}
                    color={liked ? colors.palette.secondsMain : colors.palette.neutral100}
                  />
                </TouchableOpacity>
              </Animated.View>
              <Text style={$tikTokActionText}>{likes}</Text>

              {/* Share Button */}
              <TouchableOpacity style={$tikTokActionButton} onPress={onShare}>
                <Icon icon="share" size={28} color={colors.palette.neutral100} />
              </TouchableOpacity>
              <Text style={$tikTokActionText}>Partager</Text>
            </View>

            {/* Bottom Info */}
            <View style={$tikTokBottomInfo}>
              <Text weight="bold" size="lg" style={$tikTokVideoTitle}>
                {video.title}
              </Text>

              {/* Horizontal Metrics Scroll */}
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                style={$metricsScroll}
                contentContainerStyle={$metricsContainer}
              >
                <View style={$metricItem}>
                  <Icon icon="T0clock" size={16} color={colors.palette.neutral100} />
                  <Text style={$metricText}>{formatTime(videoDuration || 0)}</Text>
                </View>
                <View style={$metricItem}>
                  <Icon icon="heart" size={16} color={colors.palette.neutral100} />
                  <Text style={$metricText}>{likes} j&apos;aime</Text>
                </View>
                <View style={$metricItem}>
                  <Icon icon="view" size={16} color={colors.palette.neutral100} />
                  <Text style={$metricText}>1.2k vues</Text>
                </View>
              </ScrollView>

              {/* Action Buttons */}
              <View style={$actionButtonsContainer}>
                {videoEnded ? (
                  <>
                    <Button
                      text="Revoir"
                      style={$rewatchButton}
                      textStyle={$rewatchButtonText}
                      onPress={handleRewatch}
                    />
                    {currentIndex < totalVideos - 1 && (
                      <Button
                        text="Vidéo suivante"
                        style={$nextVideoButton}
                        textStyle={$nextVideoButtonText}
                        onPress={onNextVideo}
                      />
                    )}
                  </>
                ) : (
                  <Text style={$videoProgress}>
                    {formatTime(currentTime)} / {formatTime(videoDuration)}
                  </Text>
                )}
              </View>
            </View>
          </Animated.View>
        </TouchableOpacity>
      </Animated.View>
    </Modal>
  )
}

// ===== Styles =====

const $root: ViewStyle = {
  paddingHorizontal: 16,
}

const $section: ViewStyle = {
  paddingTop: 8,
  paddingBottom: 16,
}

const $sectionHeader: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  marginBottom: 8,
}

const $sectionHeaderText: TextStyle = {
  marginLeft: 8,
}

const $hList: ViewStyle = {
  paddingVertical: 8,
}

const $videoCard: ViewStyle = {
  width: 260,
  marginRight: 16,
}

const $videoImage: ImageStyle = {
  width: "100%",
  height: 150,
  borderRadius: 12,
}

const $videoTitle: TextStyle = {
  marginTop: 8,
}

const $overlay: ViewStyle = {
  position: "absolute",
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  borderRadius: 12,
}

const $overlayDim: ViewStyle = {
  backgroundColor: "rgba(0,0,0,0.35)",
}

const $durationBadge: ViewStyle = {
  position: "absolute",
  top: 8,
  right: 8,
  backgroundColor: "rgba(0,0,0,0.7)",
  borderRadius: 4,
  paddingHorizontal: 8,
  paddingVertical: 4,
}

const $durationText: TextStyle = {
  color: colors.palette.neutral100,
}

const $centerPlay: ViewStyle = {
  flex: 1,
  alignItems: "center",
  justifyContent: "center",
}

const $playCircle: ViewStyle = {
  width: 50,
  height: 50,
  borderRadius: 25,
  backgroundColor: "rgba(0,0,0,0.7)",
  alignItems: "center",
  justifyContent: "center",
}

const $watchedTick: ViewStyle = {
  position: "absolute",
  right: 8,
  bottom: 8,
}

const $tickCircle: ViewStyle = {
  width: 24,
  height: 24,
  borderRadius: 12,
  backgroundColor: colors.palette.primary500,
  alignItems: "center",
  justifyContent: "center",
}

const $comingTag: ViewStyle = {
  position: "absolute",
  top: "50%",
  left: "50%",
  transform: [{ translateX: -40 }, { translateY: -15 }],
  backgroundColor: "rgba(0,0,0,0.8)",
  borderRadius: 15,
  paddingHorizontal: 10,
  paddingVertical: 6,
}

const $comingText: TextStyle = {
  color: colors.palette.neutral100,
}

// ===== TikTok-Style Video Player Styles =====

const $tikTokViewerRoot: ViewStyle = {
  flex: 1,
  backgroundColor: colors.palette.neutral800,
}

const $tikTokVideoContainer: ViewStyle = {
  flex: 1,
  position: "relative",
}

const $videoContentContainer: ViewStyle = {
  flex: 1,
  justifyContent: "center",
  alignItems: "center",
}

const $tikTokVideoImage: ImageStyle = {
  width: screenWidth,
  height: screenHeight * 0.7,
  resizeMode: "cover",
}

const $tikTokOverlay: ViewStyle = {
  position: "absolute",
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  justifyContent: "space-between",
}

const $tikTokCloseButton: ViewStyle = {
  position: "absolute",
  top: 50,
  left: 20,
  width: 40,
  height: 40,
  borderRadius: 20,
  backgroundColor: "rgba(0, 0, 0, 0.5)",
  alignItems: "center",
  justifyContent: "center",
}

const $tikTokRightActions: ViewStyle = {
  position: "absolute",
  right: 20,
  bottom: 120,
  alignItems: "center",
}

const $tikTokActionButton: ViewStyle = {
  width: 50,
  height: 50,
  borderRadius: 25,
  backgroundColor: "rgba(0, 0, 0, 0.6)",
  alignItems: "center",
  justifyContent: "center",
  marginBottom: 8,
}

const $tikTokActionText: TextStyle = {
  color: colors.palette.neutral100,
  fontSize: 12,
  textAlign: "center",
  marginBottom: 16,
}

const $tikTokBottomInfo: ViewStyle = {
  position: "absolute",
  bottom: 40,
  left: 20,
  right: 80,
}

const $tikTokVideoTitle: TextStyle = {
  color: colors.palette.neutral100,
  marginBottom: 12,
  textShadowColor: "rgba(0, 0, 0, 0.8)",
  textShadowOffset: { width: 1, height: 1 },
  textShadowRadius: 3,
}

const $metricsScroll: ViewStyle = {
  marginBottom: 16,
}

const $metricsContainer: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
}

const $metricItem: ViewStyle = {
  flexDirection: "row",
  alignItems: "center",
  backgroundColor: "rgba(0, 0, 0, 0.6)",
  paddingHorizontal: 12,
  paddingVertical: 6,
  borderRadius: 20,
  marginRight: 12,
}

const $metricText: TextStyle = {
  color: colors.palette.neutral100,
  fontSize: 12,
  marginLeft: 4,
}

const $actionButtonsContainer: ViewStyle = {
  flexDirection: "row",
  gap: 12,
  marginTop: 16,
}

const $rewatchButton: ViewStyle = {
  backgroundColor: colors.palette.primary500,
  borderRadius: 25,
  paddingVertical: 12,
  flex: 1,
}

const $rewatchButtonText: TextStyle = {
  color: colors.palette.neutral100,
  fontWeight: "600",
}

const $nextVideoButton: ViewStyle = {
  backgroundColor: colors.palette.secondary500,
  borderRadius: 25,
  paddingVertical: 12,
  flex: 1,
}

const $nextVideoButtonText: TextStyle = {
  color: colors.palette.neutral100,
  fontWeight: "600",
}

const $videoProgress: TextStyle = {
  color: colors.palette.neutral100,
  fontSize: 14,
  textAlign: "center",
}
