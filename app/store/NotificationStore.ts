import { create } from "zustand"
import { z } from "zod"
import * as Device from "expo-device"
import { Platform } from "react-native"
import axiosInstance from "@/config/axiosInstance"

const PushTokenSchema = z.object({
  token: z.string(),
  device_type: z.string(),
  device_name: z.string(),
})

type PushNotificationState = {
  pushToken: string | null
  deviceType: string
  deviceName: string
  notificationsList: []
  registerPushToken: (token: string) => Promise<void>
  clearPushToken: () => void
  getNotifications: () => Promise<void>
}

export const usePushNotificationStore = create<PushNotificationState>((set) => ({
  pushToken: null,
  deviceType: Platform.OS,
  deviceName: Device.deviceName ?? "Unknown Device",
  notificationsList: [],

  registerPushToken: async (token: string) => {
    try {
      const deviceData = {
        token,
        device_type: Platform.OS,
        device_name: Device.deviceName ?? "Unknown Device",
      }

      const validatedData = PushTokenSchema.safeParse(deviceData)
      if (!validatedData.success) {
        console.error("Invalid push token data", validatedData.error)
        return
      }

      // Register token with backend
      const response = await axiosInstance.post("/notification/push/token/", deviceData)

      if (response.status === 200 || response.status === 201) {
        set({
          pushToken: token,
          deviceType: deviceData.device_type,
          deviceName: deviceData.device_name,
        })
        console.log("Push token registered successfully")
      }
    } catch (error) {
      console.error("Failed to register push token:", error)
      throw error
    }
  },

  getNotifications: async () => {
    try {
      const response = await axiosInstance.get("/notification/fetch/")
      // console.log("notification", response.data)
      set({
        notificationsList: response.data.data,
      })
    } catch (error) {
      console.error("Failed to register push token:", error)
      throw error
    }
  },

  clearPushToken: () => {
    set({ pushToken: null })
  },
}))
