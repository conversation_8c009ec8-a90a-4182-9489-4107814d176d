/* eslint-disable import/no-unresolved */
import { create } from "zustand"
import { persist, createJSONStorage } from "zustand/middleware"
// import { storage } from "@/utils/storage"
// import axiosInstance from "@/config/axiosInstance"
import { TopUpHistory, type Transaction } from "@/services/api/api.types"
import axiosInstance from "@/config/axiosInstance"
import { storage } from "@/utils/storage"
// import { transactionSchema } from "@/services/api/api.types"

interface RecentPayment {
  id: string
  recipientName: string | null
  recipientProfile: string | null
  amount: number
  note: string
  date: string
  walletId: string
}

interface TransactionState {
  transactions: Transaction[]
  loading: boolean
  error: string | null
  currentPage: number
  fetchTransactions: () => Promise<void>
  refreshTransactions: () => Promise<void>
  getTransactionById: (id: string) => Transaction | undefined
  getPendingTransactions: () => Transaction[]
  getCompletedTransactions: () => Transaction[]
  topups: TopUpHistory[]
  topupsLoading: boolean
  topupsError: string | null
  fetchTopups: () => Promise<void>
  refreshTopups: () => Promise<void>
  getTopUpHistory: () => Promise<{ success: boolean; data?: TopUpHistory[]; error?: string }>
  getRecentPayments: (limit?: number) => RecentPayment[]
  recentPayments: RecentPayment[]
  recentPaymentsLoading: boolean
  recentPaymentsError: string | null
  fetchRecentPayments: () => Promise<RecentPayment[]>
}

export const useTransactionStore = create<TransactionState>()(
  persist(
    (set, get) => ({
      transactions: [],
      loading: false,
      error: null,
      currentPage: 1,
      hasMore: true,
      topups: [],
      topupsLoading: false,
      topupsError: null,
      recentPayments: [],
      recentPaymentsLoading: false,
      recentPaymentsError: null,

      async fetchTransactions() {
        try {
          set({ loading: true, error: null })

          const response = await axiosInstance.get<Transaction[]>(`/transaction/history/`)

          set({
            transactions: response.data,
            loading: false,
            error: null,
          })
        } catch (error: any) {
          console.error("error", error)

          // Keep existing transactions if available
          const currentTransactions = get().transactions

          set({
            error: "Impossible de mettre à jour l'historique des transactions",
            loading: false,
            transactions: currentTransactions.length ? currentTransactions : [], // Keep existing data if available
          })

          // Optionally retry once for 500 errors
          if (error.response?.status === 500) {
            setTimeout(() => {
              get().fetchTransactions()
            }, 2000) // Retry after 2 seconds
          }
        }
      },

      async refreshTransactions() {
        set({ currentPage: 1 })
        await get().fetchTransactions()
      },

      getTransactionById(id: string) {
        return get().transactions.find((t) => t.id === id)
      },

      getPendingTransactions() {
        return get().transactions.filter(
          (t) => t.status === "in_progress" || t.status === "pending",
        )
      },

      getCompletedTransactions() {
        return get().transactions.filter((t) => t.status === "completed")
      },

      async fetchTopups() {
        try {
          set({ topupsLoading: true, topupsError: null })
          const response = await axiosInstance.get("/transaction/top-up/my-requests/")

          set({
            topups: response.data,
            topupsLoading: false,
            topupsError: null,
          })
        } catch (error: any) {
          console.error("Failed to fetch top-ups:", error)
          set({
            topupsError: "Impossible de charger l'historique des recharges",
            topupsLoading: false,
            topups: [],
          })
        }
      },

      async refreshTopups() {
        try {
          set({ topupsLoading: true, topupsError: null })
          const response = await axiosInstance.get("/transaction/top-up/my-requests/")

          set({
            topups: response.data,
            topupsLoading: false,
            topupsError: null,
          })
        } catch (error: any) {
          console.error("Failed to refresh top-ups:", error)
          set({
            topupsError: "Impossible de rafraîchir l'historique des recharges",
            topupsLoading: false,
          })
        }
      },

      async getTopUpHistory() {
        try {
          const response = await axiosInstance.get("/transaction/top-up/my-requests/")
          return {
            success: true,
            data: response.data,
          }
        } catch (error: any) {
          console.error("Failed to fetch top-up history:", error)
          return {
            success: false,
            error: error.response?.data?.message || "Failed to fetch top-up history",
          }
        }
      },

      // Legacy method that gets recent payments from transactions
      getRecentPayments(limit?: number): RecentPayment[] {
        // If we have recent payments from the API, use those instead
        if (get().recentPayments.length > 0) {
          const payments = get().recentPayments
          return limit ? payments.slice(0, limit) : payments
        }

        // Fallback to the old method if API data is not available
        const transactions = get().transactions

        // First filter for completed transactions and sort by date
        const sortedTransactions = transactions
          .filter((transaction) => transaction.status === "completed")
          .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())

        // Use Map to keep only the latest transaction per receiver
        const uniqueReceivers = new Map()

        sortedTransactions.forEach((transaction) => {
          if (!uniqueReceivers.has(transaction.receiver)) {
            uniqueReceivers.set(transaction.receiver, {
              id: transaction.id,
              recipientName: transaction.to || null,
              recipientProfile: transaction.receiver_profile || null,
              amount: parseFloat(transaction.amount),
              note: transaction.note,
              date: transaction.created_at,
              walletId: transaction.receiver,
            })
          }
        })

        // Convert Map values to array and limit if specified
        const recentPayments = Array.from(uniqueReceivers.values())
        return limit ? recentPayments.slice(0, limit) : recentPayments.slice(0, 20)
      },

      // New method that fetches recent payments directly from the API
      async fetchRecentPayments() {
        try {
          set({ recentPaymentsLoading: true, recentPaymentsError: null })

          // Call the pay/recent/ endpoint
          const response = await axiosInstance.get("/pay/recent/")

          // Transform the API response to match our RecentPayment interface
          const formattedPayments: RecentPayment[] = response.data

          set({
            recentPayments: formattedPayments,
            recentPaymentsLoading: false,
            recentPaymentsError: null,
          })

          return formattedPayments
        } catch (error: any) {
          console.error("Failed to fetch recent payments:", error.response || error)

          set({
            recentPaymentsError: "Impossible de charger les paiements récents",
            recentPaymentsLoading: false,
          })

          return []
        }
      },
    }),
    {
      name: "transaction-store",
      storage: createJSONStorage(() => ({
        getItem: (key) => {
          const value = storage.getString(key)
          return value ? JSON.parse(value) : null
        },
        setItem: (key, value) => {
          storage.set(key, JSON.stringify(value))
        },
        removeItem: (key) => {
          storage.delete(key)
        },
      })),
    },
  ),
)
