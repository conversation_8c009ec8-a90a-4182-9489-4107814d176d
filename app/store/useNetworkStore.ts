/* eslint-disable @typescript-eslint/no-unused-vars */
import { create } from "zustand"
import NetInfo from "@react-native-community/netinfo"
import { useAuthStore } from "./AuthenticationStore"
import { storage } from "@/utils/storage"
import axiosInstance from "@/config/axiosInstance"
import { loadString } from "@/utils/storage"

interface NetworkState {
  isConnected: boolean
  showNetworkOverlay: boolean
  checkInternet: () => void
  retryFailedRequests: () => Promise<void>
  hideNetworkOverlay: () => void
  retryNetwork: () => void
  reloadApp: () => void
}

export const useNetworkStore = create<NetworkState>((set, get) => ({
  isConnected: true,
  showNetworkOverlay: false,

  checkInternet: async () => {
    const netInfo = await NetInfo.fetch()
    const isConnected = netInfo.isConnected ?? false
    set({ isConnected })

    if (!isConnected) {
      set({ showNetworkOverlay: true })
    } else {
      // Retry failed requests when network is back
      get().retryFailedRequests()
    }
  },

  hideNetworkOverlay: () => {
    set({ showNetworkOverlay: false })
  },

  retryNetwork: async () => {
    const netInfo = await NetInfo.fetch()
    const isConnected = netInfo.isConnected ?? false

    if (isConnected) {
      set({ isConnected: true, showNetworkOverlay: false })
      get().retryFailedRequests()
    }
  },

  reloadApp: () => {
    // This will be handled by the app component
    set({ showNetworkOverlay: false })
    // The app component can listen to this state change and reload
  },

  retryFailedRequests: async () => {
    try {
      // Prevent retry if logged out
      if (loadString("hasLoggedOut") === "true") return
      const failedRequests = storage.getString("failedRequests")
      if (!failedRequests) return

      const requests = JSON.parse(failedRequests)
      if (!requests.length) return

      // Clear failed requests immediately to prevent duplicate retries
      storage.delete("failedRequests")

      // Retry each failed request
      for (const request of requests) {
        try {
          await axiosInstance({
            url: request.url,
            method: request.method,
            data: request.data,
            headers: request.headers,
          })
        } catch (error) {
          console.error("Failed to retry request:", error)
          // If retry fails, add back to failed requests
          const currentFailed = storage.getString("failedRequests") || "[]"
          const currentRequests = JSON.parse(currentFailed)
          currentRequests.push(request)
          storage.set("failedRequests", JSON.stringify(currentRequests))
        }
      }
    } catch (error) {
      console.error("Error retrying failed requests:", error)
    }
  },
}))
