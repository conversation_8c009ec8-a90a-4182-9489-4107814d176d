import { useAuthStore } from "@/store/AuthenticationStore"
import NetInfo from "@react-native-community/netinfo"
import { useNetworkStore } from "@/store/useNetworkStore"
import { storage, loadString } from "@/utils/storage"
import axios from "axios"

const axiosInstance = axios.create({
  baseURL: process.env.DEV_API_URL || "https://stageapi.fedha.link/api",
  withCredentials: false,
  headers: {
    "Content-Type": "application/json",
  },
})

// Initialize auth header if token exists
const token = storage.getString("accessToken")
if (token) {
  axiosInstance.defaults.headers.common.Authorization = `Bearer ${token}`
}

axiosInstance.interceptors.request.use(
  async (config) => {
    const netInfo = await NetInfo.fetch()
    if (!netInfo.isConnected) {
      useNetworkStore.getState().checkInternet()
      // Store the failed request for retry when network is back
      const failedRequests = storage.getString("failedRequests") || "[]"
      const requests = JSON.parse(failedRequests)
      requests.push({
        url: config.url,
        method: config.method,
        data: config.data,
        headers: config.headers,
        timestamp: Date.now(),
      })
      storage.set("failedRequests", JSON.stringify(requests))
      return Promise.reject({ message: "No internet connection" })
    }

    // Get fresh token from storage
    const token = storage.getString("accessToken")
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => Promise.reject(error),
)

const MAX_TOKEN_ATTEMPTS = 3
let tokenRefreshInProgress = false

axiosInstance.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config
    const authStore = useAuthStore.getState()

    // Prevent retry/refresh if already logged out
    const hasLoggedOut = loadString && loadString("hasLoggedOut") === "true"
    if (hasLoggedOut) {
      return Promise.reject(error)
    }

    // Handle network errors
    if (!error.response && error.message === "Network Error") {
      console.warn("🌐 Network error detected")
      useNetworkStore.getState().checkInternet()
      return Promise.reject(error)
    }

    // Handle 401 Unauthorized errors
    if (error.response?.status === 401) {
      console.warn("🔒 Unauthorized request detected, checking token...")

      // Increment failed attempts
      const currentAttempts = authStore.failedTokenAttempts + 1
      useAuthStore.setState({ failedTokenAttempts: currentAttempts })

      // Force logout if too many failed attempts
      if (currentAttempts >= MAX_TOKEN_ATTEMPTS) {
        console.error("🔴 Too many failed token refresh attempts, forcing logout...")
        authStore.logout()
        return Promise.reject(error)
      }

      // Prevent concurrent token refresh requests
      if (tokenRefreshInProgress) {
        return Promise.reject(error)
      }

      // Prevent infinite refresh loops
      if (originalRequest._retry) {
        return Promise.reject(error)
      }

      originalRequest._retry = true
      tokenRefreshInProgress = true

      try {
        const newToken = await authStore.refreshAuthToken()
        tokenRefreshInProgress = false

        if (newToken) {
          // Reset failed attempts on successful token refresh
          useAuthStore.setState({ failedTokenAttempts: 0 })

          originalRequest.headers.Authorization = `Bearer ${newToken}`
          return axiosInstance(originalRequest)
        } else {
          // If no token returned, clear data and logout
          console.error("🔴 No token returned from refresh, logging out...")
          authStore.clearLocalData()
          authStore.logout()
          return Promise.reject(new Error("Token refresh returned no token"))
        }
      } catch (refreshError) {
        tokenRefreshInProgress = false
        console.error("🔴 Token refresh failed, logging out...")

        // Clear local data immediately when token refresh fails
        authStore.clearLocalData()
        authStore.logout()

        return Promise.reject(refreshError)
      }
    }

    return Promise.reject(error)
  },
)

export default axiosInstance
