/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable prettier/prettier */
/* eslint-disable import/no-unresolved */
import axiosInstance from "@/config/axiosInstance"
import { compressImage } from "@/utils/compresor"
import RNFS from "react-native-fs"
import { handleApiError } from "@/utils/apiErrorHandler"
import axios from "axios"
import { useAuthStore } from "@/store"

export const getFedhaPochi = async () => {
  try {
    const response = await axiosInstance.get("/wallet/my-pochi/")

    return { success: true, data: response.data.detail }
  } catch (error: any) {
    console.error("API Error:", error) // Log the full error for debugging
    return {
      success: false,
      message: error.response?.data || "An error occurred while getting FedhaPochi",
    }
  }
}

export const FedhaPochiTopUp = async (formData: FormData) => {
  try {
    const response = await axiosInstance.post("/transaction/top-up/", formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    })
    console.log("TOPUP:", response.data)
    return response.data
  } catch (error: any) {
    console.error("Error TOPING UP:", error.response?.data || error.message)
    throw error
  }
}

export const SnelPowerTopUp = async (group_id:any, data: {
  provider_id: string
  meter_no: number
  amount: number
  currency: string
  note: string
}) => { 
  try {

    console.log(`/service/request/${group_id}/`, data )

    const response = await axiosInstance.post(`/service/request/${group_id}/`, data)
  
    return { success: true, message: response?.data.details, data: response.data }
  } catch (error: any) {
    console.error("Error snel Request:", error) // Keep full error log for debugging

    // Handle specific error cases
    if (error.response) {
      // Server responded with an error status
      switch (error.response.status) {
        case 400:
          throw new Error(
            "Les informations fournies sont invalides. Veuillez vérifier et réessayer.",
          )
        case 401:
          throw new Error("Session expirée. Veuillez vous reconnecter.")
        case 403:
          throw new Error("Vous n'êtes pas autorisé à effectuer cette opération.")
        case 404:
          throw new Error("Service temporairement indisponible.")
        case 500:
          throw new Error(
            "Le service est momentanément indisponible. Veuillez réessayer plus tard.",
          )
        default:
          throw new Error("Une erreur est survenue. Veuillez réessayer plus tard.")
      }
    } else if (error.request) {
      // Request was made but no response received
      throw new Error("Impossible de joindre le serveur. Vérifiez votre connexion internet.")
    } else {
      // Something else happened while setting up the request
      throw new Error("Une erreur inattendue s'est produite. Veuillez réessayer.")
    }
  }
}

export const SnelCancelPowerTopUp = async (request_id: string) => {
  try {
    const response = await axiosInstance.post("/service/snel/token/request/cancel/", request_id)
    return { success: true, message: response?.data.details, data: response.data }
  } catch (error: any) {
    console.error("Error snel Request:", error) // Keep full error log for debugging

    // Handle specific error cases
    if (error.response) {
      // Server responded with an error status
      switch (error.response.status) {
        case 400:
          throw new Error(
            "Les informations fournies sont invalides. Veuillez vérifier et réessayer.",
          )
        case 401:
          throw new Error("Session expirée. Veuillez vous reconnecter.")
        case 403:
          throw new Error("Vous n'êtes pas autorisé à effectuer cette opération.")
        case 404:
          throw new Error("Service temporairement indisponible.")
        case 500:
          throw new Error(
            "Le service est momentanément indisponible. Veuillez réessayer plus tard.",
          )
        default:
          throw new Error("Une erreur est survenue. Veuillez réessayer plus tard.")
      }
    } else if (error.request) {
      // Request was made but no response received
      throw new Error("Impossible de joindre le serveur. Vérifiez votre connexion internet.")
    } else {
      // Something else happened while setting up the request
      throw new Error("Une erreur inattendue s'est produite. Veuillez réessayer.")
    }
  }
}




export const getTransactions = async () => {
  try {
    const response = await axiosInstance.get("/transaction/history/")
    return { success: true, data: response.data }
  } catch (error: any) {
    console.error("API Error:", error) // Log the full error for debugging
    return {
      success: false,
      message: error.response?.data || "An error occurred while updating the transaction.",
    }
  }
}

export const TransferCash = async (data: {
  receiver: string
  currency: string
  amount: number
  note: string
}) => {
  try {
    const response = await axiosInstance.post("/transaction/send/", data)
    return { success: true, data: response.data }
  } catch (error: any) {
    console.error("Error Transfer Request:", error)
    return {
      success: false,
      message: error.response?.data || "Échec du transfert" 
    }
  }
}

export const WithdrawRequest = async (data: {
  receiver_phone: string
  currency: string
  amount: number
  network: string
  note: string
}) => {
  try {
    const response = await axiosInstance.post("/transaction/send/", data)
    return { success: true, data: response.data }
  } catch (error: any) {
    console.error("Error Transfer Request:", error)
    return {
      success: false,
      message: error.response?.data || "Échec du retrait" 
    }
  }
}

export const usrlogout = async () => {
  try {
    const get = useAuthStore.getState()
   const isRefreshT =  get.refreshToken
    // Call logout API using axiosInstance
    const response = await axiosInstance.post(`/logout/?refresh=${isRefreshT}`)
    console.log("✅ Logout API call successful:", response.data)
    return { success: true, data: response.data }
  } catch (error: any) {
    console.warn("⚠️ Logout API failed:", error?.response?.data || error.message)
    return { success: false, message: error?.response?.data || "Logout failed" }
  }
}

export const Payinvoice = async (invoiceid: string) => {
  try {
    const response = await axiosInstance.post(`/pay/invoice/checkout/`,{
      cart_checkout: invoiceid
    }
    )
    // return response.data
    return {
      success: true,
      data: response.data,
    }
  } catch (error: any) {
    console.error("Error :", error.response?.data || error.message)
    // throw new Error(error.response?.data?.detail || "Failed to create transaction.")
    return {
      success: false,
      data: handleApiError(error, {
        title: "Échec de confirmation",
      }),
    }
  }
}

export const getPochiID = async (qrid: string) => {
  try {
    const response = await axiosInstance.get(`/pay/account-verification/${qrid}/`,
    )
    // return response.data
    return {
      success: true,
      data: response.data,
    }
  } catch (error: any) {
    console.error("Error :", error.response?.data || error.message)
    // throw new Error(error.response?.data?.detail || "Failed to create transaction.")
    return {
      success: false,
      data: handleApiError(error, {
        title: "Échec de confirmation",
      }),
    }
  }
}

export const confirmCashPayment = async (paymentRef: any) => {
  try {
    const response = await axiosInstance.patch(`/pay/invoice/${paymentRef}/cash/`)
    return { success: true, data: response.data }
  } catch (error: any) {
    console.error("Error Transfer Request:", error)
    return {
      success: false,
      message: handleApiError(error, {
        title: "Échec de confirmation",
      }),
    }
  }
}

export const getServiceHistory = async (servicename: string) => {
  try {
    const response = await axiosInstance.get(`/service/request/${servicename}/`)
    return { success: true, data: response.data }
  } catch (error: any) {
    console.error("API Error:", error) // Log the full error for debugging
    return {
      success: false,
      message: error.response?.data || "An error occurred while updating serviceHistory",
    }
  }
}

export const getUserByPochiID = async (pochiID: string) => {
  try {
    const response = await axiosInstance.get(`/pay/account-verification/${pochiID}/`)
    return { success: true, data: response.data }
  } catch (error: any) {
    console.error("API Error:", error) // Log the full error for debugging
    return {
      success: false,
      message: error.response?.data || "An error occurred while getting user detail",
    }
  }
}

export const userBasicInfoUpdate = async (usrData: FormData) => {
  try {
    const response = await axiosInstance.put("/user/update/basic/", usrData, {
      headers: {
        "Content-Type": "multipart/form-data", // Explicitly set the content type
      },
    })

    return { success: true, data: response.data.detail }
  } catch (error: any) {
    console.error("API Error:", error) // Log the full error for debugging
    return {
      success: false,
      message: error.response?.data || "An error occurred while updating the profile.",
    }
  }
}

export const userProfilePictureUpdate = async (usrData: FormData) => {
  try {
    const response = await axiosInstance.put("/user/update/basic/", usrData, {
      headers: {
        "Content-Type": "multipart/form-data", // Explicitly set the content type
      },
    })

    return { success: true, data: response.data.detail }
  } catch (error: any) {
    console.error("API Error:", error) // Log the full error for debugging
    return {
      success: false,
      message: error.response?.data || "An error occurred while updating the profile.",
    }
  }
}

export const userAdvancedInfoUpdate = async (usrData: FormData) => {
  try {
    const response = await axiosInstance.put("/user/update/advance/", usrData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    })
    return { success: true, data: response.data }
  } catch (error: any) {
    console.error("API Error:", error)

    // Handle specific error cases
    if (error.response) {
      switch (error.response.status) {
        case 400:
          return {
            success: false,
            message: "Les informations fournies sont invalides. Veuillez vérifier vos données.",
          }
        case 401:
          return {
            success: false,
            message: "Session expirée. Veuillez vous reconnecter.",
          }
        case 502:
          return {
            success: false,
            message: "Le service est temporairement indisponible. Veuillez réessayer plus tard.",
          }
        case 500:
          return {
            success: false,
            message: "Une erreur serveur s'est produite. Veuillez réessayer plus tard.",
          }
        default:
          return {
            success: false,
            message: error.response.data?.message || "Une erreur inattendue s'est produite.",
          }
      }
    } else if (error.request) {
      return {
        success: false,
        message: "Impossible de joindre le serveur. Vérifiez votre connexion internet.",
      }
    }

    return {
      success: false,
      message: "Une erreur s'est produite lors de la mise à jour du profil.",
    }
  }
}

export const uploadDocument = async (data: FormData) => {
  try {
    // Extract the file object from FormData
    const fileEntry = Array.from(data.entries()).find(([key]) => key === "piece_identite")

    if (!fileEntry || !fileEntry[1]) {
      throw new Error("No file found in FormData")
    }

    const file = fileEntry[1] as any

    // Validate file URI
    if (!file.uri) {
      throw new Error("Invalid file: missing URI")
    }

    // Debug: Log file details
    console.log('File details:', {
      uri: file.uri,
      type: file.type,
      name: file.name
    })

    // Verify file exists before compression
    const fileExists = await RNFS.exists(file.uri)
    if (!fileExists) {
      throw new Error("File does not exist at specified URI")
    }

    // Compress the image and create new FormData
    const compressedUri = await compressImage(file.uri, {
      maxWidth: 1024,
      quality: 0.6,
    })

    if (!compressedUri) {
      throw new Error("Image compression failed")
    }

    // Debug: Check compressed file size
    const compressedFileInfo = await RNFS.stat(compressedUri)
    console.log('Final upload size:', (compressedFileInfo.size / (1024 * 1024)).toFixed(2), 'MB')

    // Create new FormData with compressed image
    const compressedFormData = new FormData()

    // Add the compressed file
    compressedFormData.append("piece_identite", {
      uri: compressedUri,
      type: "image/jpeg",
      name: file.name || "document.jpg",
    } as any)

    // Copy other fields from original FormData
    Array.from(data.entries()).forEach(([key, value]) => {
      if (key !== "piece_identite") {
        compressedFormData.append(key, value)
      }
    })

    // Make the API request with compressed data
    const response = await axiosInstance.put("/user/update/document/", compressedFormData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
      timeout: 30000, // 30 seconds timeout
    })

    return { success: true, data: response.data }
  } catch (error: any) {
    console.error("Document Upload Error:", error)
    console.error("Error response data:", error.response?.data)

    if (error.message === "Network Error") {
      return {
        success: false,
        message: "Erreur de connexion. Vérifiez votre connexion internet.",
      }
    }

    if (error.response) {
      switch (error.response.status) {
        case 413:
          return {
            success: false,
            message: "Le fichier reste trop volumineux. Veuillez utiliser une image de plus petite taille.",
          }
        case 400:
          return {
            success: false,
            message: "Format de document invalide. Veuillez réessayer.",
          }
        case 500:
          return {
            success: false,
            message: "Erreur serveur. Veuillez réessayer plus tard.",
          }
        default:
          return {
            success: false,
            message: error.response.data?.message || "Erreur lors du téléchargement.",
          }
      }
    }

    return {
      success: false,
      message: error.message || "Erreur lors du téléchargement du document.",
    }
  }
}

// export const getSNELHistory = async () => {
//   try {
//     const response = await axiosInstance.get(`/service/my/requests/sneldrc/`)
//     return { success: true, data: response.data }
//   } catch (error: any) {
//     console.error("API Error:", error) // Log the full error for debugging
//     return {
//       success: false,
//       message: error.response?.data || "An error occurred while getting user snel history",
//     }
//   }
// }

export const getCurrencies = async () => {
  try {
    const response = await axiosInstance.get(`/bank/currencies/`)
    return { success: true, data: response.data }
  } catch (error: any) {
    console.error("API Error:", error) // Log the full error for debugging
    return {
      success: false,
      message: error.response?.data || "An error occurred while getting user snel history",
    }
  }
}

export const getBeneficiers = async () => {
  try {
    const response = await axiosInstance.get(`/transaction/beneficiary/`)
    return { success: true, data: response.data }
  } catch (error: any) {
    console.error("API Error:", error) // Log the full error for debugging
    return {
      success: false,
      message: error.response?.data || "An error occurred while getting beneficierires ",
    }
  }
}

export const RegisterUsrDeviceNoticiation = async (data: any) => {
  try {
    const response = await axiosInstance.post(`/notification/push/token/`, data)
    console.log("NOTIFICATION TOKEN:", response.data)
    return { success: true, data: response.data }
  } catch (error: any) {
    console.error("API Error:", error) // Log the full error for debugging
    
    // Handle specific error cases
    if (error.response?.status === 404) {
      console.warn("Push token endpoint not found (404) - this might be expected in development")
      return {
        success: false,
        message: "Push token endpoint not available",
      }
    }
    
    return {
      success: false,
      message: error.response?.data || "An error occurred while sending the notification TOKEN",
    }
  }
}

export const getAccountList = async () => {
  try {
    const response = await axiosInstance.get(`/bank/fedha/external/account/`)
    return { success: true, data: response.data }
  } catch (error: any) {
    console.error("API Error:", error) // Log the full error for debugging
    return {
      success: false,
      message: error.response?.data || "An error occurred while getting MobileWallet ",
    }
  }
}

export const getFAQData = async () => {
  try {
    const response = await axiosInstance.get(`/management/faq/`)
    return { success: true, data: response.data }
  } catch (error: any) {
    console.error("API Error:", error) // Log the full error for debugging
    return {
      success: false,
      message: error.response?.data || "An error occurred while getting FAQ data",
    }
  }
}

export const Forgetpassword = async (data: any) => {
  try {
    const response = await axiosInstance.post(`/password/forget/`, data)
    console.log("forgetpass:", response.data)
    return { success: true, data: response.data }
  } catch (error: any) {
    console.error("forgetpassError:", error)

    // Use proper error handling instead of exposing raw error data
    if (error.response) {
      const { status, data } = error.response

      switch (status) {
        case 400:
          return {
            success: false,
            message: "Les informations fournies sont invalides. Veuillez vérifier vos données.",
          }
        case 404:
          return {
            success: false,
            message: "Aucun compte trouvé avec ces informations.",
          }
        case 429:
          return {
            success: false,
            message: "Trop de tentatives. Veuillez patienter quelques minutes.",
          }
        case 500:
        case 502:
        case 503:
        case 504:
          return {
            success: false,
            message: "Le service est momentanément indisponible. Veuillez réessayer plus tard.",
          }
        default:
          return {
            success: false,
            message: "Une erreur inattendue s'est produite. Veuillez réessayer.",
          }
      }
    } else if (error.request) {
      return {
        success: false,
        message: "Impossible de joindre le serveur. Vérifiez votre connexion internet.",
      }
    } else {
      return {
        success: false,
        message: "Une erreur inattendue s'est produite. Veuillez réessayer.",
      }
    }
  }
}
export const Restpassword = async (data: any) => {
  try {
    const response = await axiosInstance.post(`/password/reset/`, data)
    console.log("resetpass:", response.data)
    return { success: true, data: response.data }
  } catch (error: any) {
    console.error("resetpassError:", error)

    // Use proper error handling instead of exposing raw error data
    if (error.response) {
      const { status, data } = error.response

      switch (status) {
        case 400:
          return {
            success: false,
            message: "Les informations fournies sont invalides. Veuillez vérifier vos données.",
          }
        case 401:
          return {
            success: false,
            message: "Code de réinitialisation invalide ou expiré.",
          }
        case 404:
          return {
            success: false,
            message: "Aucun compte trouvé avec ces informations.",
          }
        case 429:
          return {
            success: false,
            message: "Trop de tentatives. Veuillez patienter quelques minutes.",
          }
        case 500:
        case 502:
        case 503:
        case 504:
          return {
            success: false,
            message: "Le service est momentanément indisponible. Veuillez réessayer plus tard.",
          }
        default:
          return {
            success: false,
            message: "Une erreur inattendue s'est produite. Veuillez réessayer.",
          }
      }
    } else if (error.request) {
      return {
        success: false,
        message: "Impossible de joindre le serveur. Vérifiez votre connexion internet.",
      }
    } else {
      return {
        success: false,
        message: "Une erreur inattendue s'est produite. Veuillez réessayer.",
      }
    }
  }
}


/**
 * Get personnel list for a specific business
 * @param businessId The business ID to fetch personnel for
 * @returns Personnel list response
 */
export const getPersonnelList = async (businessId: string) => {
  try {
    const response = await axiosInstance.get(`/business/personnel/${businessId}/view/`)
    return { success: true, data: response.data }
  } catch (error: any) {
    console.error("API Error:", error) // Log the full error for debugging
    return {
      success: false,
      message: error.response?.data || "An error occurred while getting personnel list",
    }
  }
}

/**
 * Add new personnel to a business
 * @param data Object containing business ID, personnel ID, and permissions
 * @returns Response from the API
 */
export const addPersonnel = async (data: {
  business: string;
  personnel: string;
  permissions: string[];
}) => {
  try {
    const response = await axiosInstance.post(`/business/personnel/new/`, data)
    return {
      success: true,
      data: response.data,
      message: "Personnel added successfully"
    }
  } catch (error: any) {
    console.error("API Error:", error) // Log the full error for debugging
    return {
      success: false,
      message: error.response?.data?.message || "An error occurred while adding personnel",
    }
  }
}

export const CreateBusiness = async (data: any) => {
  try {
    // Set the appropriate headers for FormData
    const headers = data instanceof FormData ? { "Content-Type": "multipart/form-data" } : {}

    console.log('Sending data to API:', data instanceof FormData ? 'FormData' : data)

    // Make the API request with proper headers
    const response = await axiosInstance.post(`/business/create/`, data, {
      headers,
    })
    console.log("Business Created:", response.data)

    // Check if the response contains an error message
    if (response.data && response.data.error) {
      return {
        success: false,
        data: response.data,
        message: response.data.error
      }
    }

    return {
      success: true,
      data: response.data,
      message: "Business created successfully"
    }
  } catch (error: any) {
    console.error("API Error:", error)
    return {
      success: false,
      data: error.response?.data,
      message: error.response?.data?.error || error.message || "An error occurred while creating business"
    }
  }
}

export const CreateBusinessProduct = async (businessID: any, formdata: any) => {
  // business/products/BS-4589710/create/
  const headers = formdata instanceof FormData ? { "Content-Type": "multipart/form-data" } : {}
  console.log('APISEND', businessID, formdata )

  try {
    const response = await axiosInstance.post(`/business/products/${businessID}/create/`, formdata, {
      headers,
    })

    console.log("Business Created:", response.data)

    // Check if the response contains an error message
    if (response.data && response.data.error) {
      return {
        success: false,
        response: response.data,
        message: response.data.error || "Une erreur s'est produite"
      }
    }

    return {
      success: true,
      data: response.data,
      message: "Products created successfully"
    }
  } catch (error: any) {
    console.error("API Error:", error)
    return {
      success: false,
      data: error.response?.data,
      message: error.response?.data?.error || error.message || "An error occurred while creating business"
    }
  }
}

const NUMVERIFY_API_KEY = "********************************"

export const checkProvider = async (phoneNumber: string): Promise<string | null> => {
  try {
    const response = await axios.get("http://apilayer.net/api/validate", {
      params: {
        access_key: NUMVERIFY_API_KEY,
        number: `243${phoneNumber}`, // Add DRC code
        country_code: "CD",
        format: 1,
      },
    })

    if (response.data.valid) {
      return response.data.carrier || "Unknown"
    } else {
      return "Invalid number"
    }
  } catch (error) {
    console.error("Error validating number:", error)
    return null
  }
}
