/* eslint-disable @typescript-eslint/no-unused-vars */
import * as Device from "expo-device"
import * as Notifications from "expo-notifications"
import { Platform } from "react-native"
import Constants from "expo-constants"
import { colors } from "@/theme"
import { RegisterUsrDeviceNoticiation } from "@/services/api"

/**
 * Register for push notifications
 * @param registerWithBackend Whether to register the token with the backend API
 * @returns The push token string or undefined if registration failed
 */
export async function registerForPushNotificationsAsync(
  registerWithBackend: boolean = true,
): Promise<string | undefined> {
  if (Platform.OS === "android") {
    await Notifications.setNotificationChannelAsync("default", {
      name: "default",
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: colors.palette.neutral900,
      sound: "notificationsound.wav", // Use custom sound for Android notifications
    })
  }

  if (Device.isDevice) {
    const { status: exitingStatus } = await Notifications.getPermissionsAsync()
    let finalStatus = exitingStatus
    if (exitingStatus !== "granted") {
      const { status } = await Notifications.requestPermissionsAsync()
      finalStatus = status
    }
    if (finalStatus !== "granted") {
      throw new Error("Failed to get push token for push notification!")
    }

    const projectId =
      Constants?.expoConfig?.extra?.eas?.projectId ?? Constants?.easConfig?.projectId
    if (!projectId) {
      throw new Error("No Expo project ID found")
    }

    try {
      const pushTokenString = (
        await Notifications.getExpoPushTokenAsync({
          projectId,
        })
      ).data
      console.log("Push token:", pushTokenString)

      // Only register with backend if requested
      if (registerWithBackend) {
        try {
          const registerPushToken = await RegisterUsrDeviceNoticiation({
            push_token: pushTokenString,
          })

          if (registerPushToken.success) {
            console.log("Push token registered with backend successfully")
          } else {
            console.warn(
              "Push token registration returned success: false",
              registerPushToken.message,
            )
            // Don't throw error here, just log the warning
          }
        } catch (apiError) {
          console.error("Failed to register push token with backend:", apiError)
          // Don't throw error here, just log it and continue
          // The token is still valid for local notifications
        }
      } else {
        console.log("Skipping backend registration as requested")
      }

      return pushTokenString
    } catch (error: unknown) {
      console.error("Error getting push token:", error)
      throw new Error(`${error}`)
    }
  }

  // If we're not on a physical device, return undefined
  console.log("Not on a physical device, skipping push notification registration")
  return undefined
}
