// import * as Notifications from "expo-notifications"
// import { Platform } from "react-native"
// import { usePushNotificationStore } from "@/store/NotificationStore"
// import axiosInstance from "@/config/axiosInstance"
// import { registerForPushNotificationsAsync } from "./registerForPushNotificationsAsync"

// export const scheduleWelcomeNotification = async () => {
//   await Notifications.scheduleNotificationAsync({
//     content: {
//       title: "Bienvenue sur Fedha! 👋",
//       body: "Merci d'avoir installé notre application. Découvrez comment gérer votre argent plus intelligemment.",
//       data: { type: "welcome" }, // This is important for identifying the notification type
//     },
//     trigger: null,
//     // trigger: {
//       // type: "timeIntervalTrigger",
//     //   seconds: 5, // Show notification 5 seconds after app first opens
//     // },
//   })
// }

// export const showTestNotification = async () => {
//   await Notifications.scheduleNotificationAsync({
//     content: {
//       title: "Test Notification 🔔",
//       body: "Ceci est une notification de test pour le mode développement",
//       data: { type: "test" },
//     },
//     trigger: null, // null means show immediately
//   })
// }

// // Add more notification types as needed
// export const showRechargementNotification = async (amount: string, type: "credit" | "debit") => {
//   await Notifications.scheduleNotificationAsync({
//     content: {
//       title: type === "credit" ? "Rechargement Reçu 💰" : "Rechrgement Encour ✨",
//       body: `Rechargement de ${amount} ${type === "credit" ? "créditée" : "débitée"} sur votre compte`,
//       data: { type: "transaction", transactionType: type },
//     },
//     trigger: null,
//   })
// }

// export const showElectricityPurchaseNotification = async (
//   amount: string,
//   meterNo: string,
//   status: "success" | "failed",
// ) => {
//   await Notifications.scheduleNotificationAsync({
//     content: {
//       title:
//         status === "success"
//           ? "Achat d'électricité réussi ⚡"
//           : "Échec de l'achat d'électricité ❌",
//       body:
//         status === "success"
//           ? `Votre achat d'électricité de ${amount} pour le compteur ${meterNo} a été effectué avec succès.`
//           : `L'achat d'électricité de ${amount} pour le compteur ${meterNo} a échoué. Veuillez réessayer.`,
//       data: { type: "electricity", status, meterNo },
//     },
//     trigger: null, // Show immediately
//   })
// }

// export const testPushNotification = async () => {
//   try {
//     // 1. Get the token and register device
//     const pushTokenData = await registerAndVerifyPushToken()
//     console.log("📱 Device registered successfully:", pushTokenData)

//     // 2. Test local notification
//     await showTestNotification()

//     // 3. Test backend-triggered notification
//     await testBackendNotification(pushTokenData.token)

//     return pushTokenData
//   } catch (error) {
//     console.error("❌ Push notification test failed:", error)
//     throw error
//   }
// }

// export const registerAndVerifyPushToken = async () => {
//   try {
//     // Get the token
//     const token = await registerForPushNotificationsAsync()

//     const store = usePushNotificationStore.getState()

//     const deviceData = {
//       token,
//       device_type: Platform.OS,
//       device_name: store.deviceName,
//       status: "active",
//     }
//     // Register with our backend

//     await store.registerPushToken(token)

//     return deviceData
//   } catch (error) {
//     console.error("❌ Failed to register push token:", error)
//     throw error
//   }
// }

// export const testBackendNotification = async (token: string) => {
//   try {
//     // Call backend endpoint to trigger a test notification
//     const response = await axiosInstance.post("/notification/test/", {
//       token,
//       title: "Test from Backend 🚀",
//       body: "This notification was triggered by the backend!",
//       data: { type: "backend_test" },
//     })

//     console.log("✅ Backend notification triggered:", response.data)
//     return response.data
//   } catch (error) {
//     console.error("❌ Backend notification test failed:", error)
//     throw error
//   }
// }
